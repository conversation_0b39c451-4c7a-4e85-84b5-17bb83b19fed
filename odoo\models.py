# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Backward compatibility layer for Odoo models.

This file maintains backward compatibility with the original monolithic models.py
while delegating to the new modular structure in odoo/models/ package.

IMPORTANT: This file should not be modified directly. All model functionality
has been moved to the odoo/models/ package for better maintainability.
"""

from __future__ import annotations

import collections
import contextlib
import datetime
import functools
import inspect
import itertools
import io
import json
import logging
import operator
import pytz
import re
import uuid
import warnings
from collections import defaultdict, deque
from collections.abc import MutableMapping, Callable
from contextlib import closing
from inspect import getmembers
from operator import attrgetter, itemgetter

import babel
import babel.dates
import dateutil.relativedelta
import psycopg2
import psycopg2.extensions
from psycopg2.extras import Json

import odoo
from . import SUPERUSER_ID
from . import api
from . import tools
from .api import NewId
from .exceptions import AccessError, MissingError, ValidationError, UserError
from .tools import (
    clean_context, config, date_utils, discardattr,
    DEFAULT_SERVER_DATE_FORMAT, DEFAULT_SERVER_DATETIME_FORMAT, format_list,
    frozendict, get_lang, lazy_classproperty, OrderedSet,
    ormcache, partition, Query, split_every, unique,
    SQL, sql, groupby,
)
from .tools.lru import LRU
from .tools.misc import LastOrderedSet, ReversedIterable, unquote
from .tools.translate import _, LazyTranslate

import typing
if typing.TYPE_CHECKING:
    from collections.abc import Reversible
    from .modules.registry import Registry
    from odoo.api import Self, ValuesType, IdType

# Import everything from the new modular structure
from .models import *

_lt = LazyTranslate('base')
_logger = logging.getLogger(__name__)
_unlink = logging.getLogger(__name__ + '.unlink')

# Constants and regex patterns from original models.py
regex_alphanumeric = re.compile(r'^[a-z0-9_]+$')
regex_order = re.compile(r'''
    ^
    (\s*
        (?P<term>((?P<field>[a-z0-9_]+|"[a-z0-9_]+")(\.(?P<property>[a-z0-9_]+))?(:(?P<func>[a-z_]+))?))
        (\s+(?P<direction>desc|asc))?
        (\s+(?P<nulls>nulls\ first|nulls\ last))?
        \s*
        (,|$)
    )+
    (?<!,)
    $
''', re.IGNORECASE | re.VERBOSE)
regex_object_name = re.compile(r'^[a-z0-9_.]+$')
regex_pg_name = re.compile(r'^[a-z_][a-z0-9_$]*$', re.I)
regex_field_agg = re.compile(r'(\w+)(?::(\w+)(?:\((\w+)\))?)?')  # For read_group
regex_read_group_spec = re.compile(r'(\w+)(\.(\w+))?(?::(\w+))?$')  # For _read_group
regex_private = re.compile(r'^(_.*|init)$')

AUTOINIT_RECALCULATE_STORED_FIELDS = 1000
GC_UNLINK_LIMIT = 100_000
INSERT_BATCH_SIZE = 100
UPDATE_BATCH_SIZE = 100
SQL_DEFAULT = psycopg2.extensions.AsIs("DEFAULT")
PREFETCH_MAX = 1000

# Utility functions from original models.py

def parse_read_group_spec(spec: str) -> tuple:
    """ Return a triplet corresponding to the given groupby/path/aggregate specification. """
    res_match = regex_read_group_spec.match(spec)
    if not res_match:
        raise ValueError(
            f'Invalid aggregate/groupby specification {spec!r}.\n'
            '- Valid aggregate specification looks like "<field_name>:<agg>" example: "quantity:sum".\n'
            '- Valid groupby specification looks like "<no_datish_field_name>" or "<datish_field_name>:<granularity>" example: "date:month" or "<properties_field_name>.<property>:<granularity>".'
        )

    groups = res_match.groups()
    return groups[0], groups[2], groups[3]


def check_object_name(name):
    """ Check if the given name is a valid model name.

        The _name attribute in osv and osv_memory object is subject to
        some restrictions. This function returns True or False whether
        the given name is allowed or not.

        TODO: this is an approximation. The goal in this approximation
        is to disallow uppercase characters (in some places, we quote
        table/column names and in other not, which leads to this kind
        of errors:

            psycopg2.ProgrammingError: relation "xxx" does not exist).

        The same restriction should apply to both osv and osv_memory
        objects for consistency.

    """
    if regex_object_name.match(name) is None:
        return False
    return True


def raise_on_invalid_object_name(name):
    if not check_object_name(name):
        msg = "The _name attribute %s is not valid." % name
        raise ValueError(msg)


def check_pg_name(name):
    """ Check whether the given name is a valid PostgreSQL identifier name. """
    if not regex_pg_name.match(name):
        raise ValidationError("Invalid characters in table name %r" % name)
    if len(name) > 63:
        raise ValidationError("Table name %r is too long" % name)


def check_method_name(name):
    """ Raise an ``AccessError`` if ``name`` is a private method name. """
    if regex_private.match(name):
        raise AccessError(_lt('Private methods (such as %s) cannot be called remotely.', name))


# Import from utils to avoid circular imports
from .models.utils import check_property_field_value_name


def fix_import_export_id_paths(fieldname):
    """
    Fixes the id fields in import and exports, and splits field paths
    on '/'.

    :param str fieldname: name of the field to import/export
    :return: split field name
    :rtype: list of str
    """
    fixed_db_id = re.sub(r'([^/])\.id', r'\1/.id', fieldname)
    fixed_external_id = re.sub(r'([^/]):id', r'\1/id', fixed_db_id)
    return fixed_external_id.split('/')


def to_company_ids(companies):
    if isinstance(companies, BaseModel):
        return companies.ids
    elif isinstance(companies, (list, tuple, str)):
        return companies
    return [companies]


def check_company_domain_parent_of(self, companies):
    """ A `_check_company_domain` function that lets a record be used if either:
        - record.company_id = False (which implies that it is shared between all companies), or
        - record.company_id is a parent of any of the given companies.
    """
    if isinstance(companies, str):
        return ['|', ('company_id', '=', False), ('company_id', 'parent_of', companies)]

    companies = [id for id in to_company_ids(companies) if id]
    if not companies:
        return [('company_id', '=', False)]

    return ['|', ('company_id', '=', False), ('company_id', 'parent_of', companies)]


def check_companies_domain_parent_of(self, companies):
    """ A `_check_company_domain` function that lets a record be used if
        record.company_ids is empty or if any of record.company_ids is a parent of any of the given companies.
    """
    if isinstance(companies, str):
        return ['|', ('company_ids', '=', False), ('company_ids', 'parent_of', companies)]

    companies = [id for id in to_company_ids(companies) if id]
    if not companies:
        return [('company_ids', '=', False)]

    return ['|', ('company_ids', '=', False), ('company_ids', 'parent_of', companies)]


# Additional utility functions for backward compatibility
def origin_ids(ids):
    """ Return an iterator over the origin ids corresponding to ``ids``. """
    for id in ids:
        if isinstance(id, NewId):
            if id.origin:
                yield id.origin
        else:
            yield id


class OriginIds:
    """ A reversible collection of record ids, with origin ids only. """
    __slots__ = ['ids']

    def __init__(self, ids):
        self.ids = ids

    def __iter__(self):
        return origin_ids(self.ids)

    def __reversed__(self):
        return origin_ids(reversed(self.ids))


def expand_ids(id0, ids):
    """ Return an iterator of unique ids from the concatenation of ``[id0]`` and
    ``ids``, and of the same kind (all origin ids or all new ids).
    """
    yield id0
    seen = {id0}
    for id in ids:
        if id not in seen:
            yield id
            seen.add(id)


def is_definition_class(cls):
    """ Return whether ``cls`` is a model definition class. """
    return isinstance(cls, type) and hasattr(cls, '_name')


def is_registry_class(cls):
    """ Return whether ``cls`` is a model registry class. """
    return getattr(cls, 'pool', None) is not None


def itemgetter_tuple(items):
    """ Fixes itemgetter inconsistency (useful in some cases) of not returning
    a tuple if len(items) == 1: always return an n-tuple where n = len(items)
    """
    if len(items) == 0:
        return lambda a: ()
    if len(items) == 1:
        return lambda gettable: (gettable[items[0]],)
    return itemgetter(*items)


# PostgreSQL error conversion functions
def convert_pgerror_not_null(model, fields, info, e):
    """Convert PostgreSQL not-null error to user-friendly message."""
    env = model.env
    field_name = e.diag.column_name
    field = fields.get(field_name)
    if field:
        message = _(
            "The field %(field_name)s is required. Please provide a value.",
            field_name=field.string or field_name
        )
    else:
        message = _(
            "A required field is missing. Please check your data."
        )
    raise ValidationError(message) from e


def convert_pgerror_unique(model, fields, info, e):
    """Convert PostgreSQL unique constraint error to user-friendly message."""
    # Extract constraint name from error
    constraint_name = e.diag.constraint_name

    # Look up the constraint in model's _sql_constraints
    sql_constraints = dict([
        (f'{e.diag.table_name}_{x[0]}', x)
        for x in model._sql_constraints
    ])

    if constraint_name in sql_constraints:
        _, _, message = sql_constraints[constraint_name]
        raise ValidationError(message) from e
    else:
        # Generic unique constraint error
        message = _(
            "The record you are trying to create already exists. "
            "Please check for duplicates."
        )
        raise ValidationError(message) from e


def convert_pgerror_constraint(model, fields, info, e):
    """Convert PostgreSQL constraint error to user-friendly message."""
    constraint_name = e.diag.constraint_name

    # Look up the constraint in model's _sql_constraints
    sql_constraints = dict([
        (f'{e.diag.table_name}_{x[0]}', x)
        for x in model._sql_constraints
    ])

    if constraint_name in sql_constraints:
        _, _, message = sql_constraints[constraint_name]
        raise ValidationError(message) from e
    else:
        # Generic constraint error
        message = _(
            "The operation cannot be completed due to data constraints. "
            "Please check your data."
        )
        raise ValidationError(message) from e


# Error mapping for PostgreSQL errors
PGERROR_TO_OE = defaultdict(
    # shape of mapped converters
    lambda: (lambda model, fvg, info, pgerror: {'message': tools.exception_to_unicode(pgerror)}),
    {
        '23502': convert_pgerror_not_null,
        '23505': convert_pgerror_unique,
        '23514': convert_pgerror_constraint,
    },
)

# Additional constants needed by other modules
READ_GROUP_NUMBER_GRANULARITY = {
    'day': 'day',
    'week': 'week',
    'month': 'month',
    'quarter': 'quarter',
    'year': 'year',
}

# Keep these imports here to avoid dependency cycle errors
# pylint: disable=wrong-import-position
from . import fields
from .osv import expression
from .fields import Field, Datetime, Command
