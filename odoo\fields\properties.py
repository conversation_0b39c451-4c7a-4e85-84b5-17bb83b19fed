# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

""" Properties field types and Command class for relational fields. """
from __future__ import annotations

import enum
import typing

from .base import Field
from ..tools.misc import Sentinel, SENTINEL


class Command(enum.IntEnum):
    """
    :class:`~odoo.fields.One2many` and :class:`~odoo.fields.Many2many` fields
    expect a special command to manipulate the relation they implement.

    Internally, each command is a 3-elements tuple where the first element is a
    mandatory integer that identifies the command, the second element is either
    the related record id to apply the command on (commands update, delete,
    unlink and link) either 0 (commands create, clear and set), the third
    element is either the ``values`` to write on the record (commands create
    and update) either the new ``ids`` list of related records (command set),
    either 0 (commands delete, unlink, link, and clear).

    Via Python, we encourage developers craft new commands via the various
    functions of this namespace. We also encourage developers to use the
    command identifier constant names when comparing the 1st element of
    existing commands.

    Via RPC, it is impossible nor to use the functions nor the command constant
    names. It is required to instead write the literal 3-elements tuple where
    the first element is the integer identifier of the command.
    """

    CREATE = 0
    UPDATE = 1
    DELETE = 2
    UNLINK = 3
    LINK = 4
    CLEAR = 5
    SET = 6

    @classmethod
    def create(cls, values: dict):
        """
        Create new records in the comodel using ``values``, link the created
        records to ``self``.

        In case of a :class:`~odoo.fields.Many2many` relation, one unique
        new record is created in the comodel such that all records in `self`
        are linked to the new record.

        In case of a :class:`~odoo.fields.One2many` relation, one new record
        is created in the comodel for every record in ``self`` such that every
        record in ``self`` is linked to exactly one of the new records.

        Return the command triple :samp:`(CREATE, 0, {values})`
        """
        return (cls.CREATE, 0, values)

    @classmethod
    def update(cls, id: int, values: dict):
        """
        Write ``values`` on the related record.

        Return the command triple :samp:`(UPDATE, {id}, {values})`
        """
        return (cls.UPDATE, id, values)

    @classmethod
    def delete(cls, id: int):
        """
        Remove the related record from the database and remove its relation
        with ``self``.

        In case of a :class:`~odoo.fields.Many2many` relation, removing the
        record from the database may be prevented if it is still linked to
        other records.

        Return the command triple :samp:`(DELETE, {id}, 0)`
        """
        return (cls.DELETE, id, 0)

    @classmethod
    def unlink(cls, id: int):
        """
        Remove the relation between ``self`` and the related record.

        In case of a :class:`~odoo.fields.One2many` relation, the given record
        is deleted from the database if the inverse field is set as
        ``ondelete='cascade'``. Otherwise, the value of the inverse field is
        set to False and the record is kept.

        Return the command triple :samp:`(UNLINK, {id}, 0)`
        """
        return (cls.UNLINK, id, 0)

    @classmethod
    def link(cls, id: int):
        """
        Add a relation between ``self`` and the related record.

        Return the command triple :samp:`(LINK, {id}, 0)`
        """
        return (cls.LINK, id, 0)

    @classmethod
    def clear(cls):
        """
        Remove all records from the relation with ``self``. It behaves like
        executing the `unlink` command on every record.

        Return the command triple :samp:`(CLEAR, 0, 0)`
        """
        return (cls.CLEAR, 0, 0)

    @classmethod
    def set(cls, ids: list):
        """
        Replace the current relations of ``self`` by the given ones. It behaves
        like executing the ``unlink`` command on every removed relation then
        executing the ``link`` command on every new relation.

        Return the command triple :samp:`(SET, 0, {ids})`
        """
        return (cls.SET, 0, ids)


class Properties(Field):
    """ Field that contains a list of properties (aka "sub-field") based on
    a definition defined on a container. Properties are pseudo-fields, acting
    like Odoo fields but without being independently stored in database.

    This field allows a light customization based on a container record. Used
    for relationships such as <project.project> / <project.task>,... New
    properties can be created on the fly without changing the structure of the
    database.

    The "definition_record" define the field used to find the container of the
    properties. The "definition_record_field" define the field on the container
    that contains the properties definition.

    Example:
        class ProjectTask(models.Model):
            _name = 'project.task'

            project_id = fields.Many2one('project.project')
            properties = fields.Properties(
                definition_record='project_id',
                definition_record_field='task_properties_definition'
            )

        class Project(models.Model):
            _name = 'project.project'

            task_properties_definition = fields.PropertiesDefinition()

    In this example, the properties field on project.task will be based on the
    definition stored in the task_properties_definition field of the related
    project.project record.
    """
    type = 'properties'
    _column_type = ('jsonb', 'jsonb')

    copy = False                        # properties are not copied by default
    prefetch = False                    # not prefetched by default

    definition = None
    definition_record = None         # field on the current model that point to the definition record
    definition_record_field = None   # field on the definition record which defined the Properties field definition

    ALLOWED_TYPES = (
        # standard types
        'boolean', 'integer', 'float', 'char', 'date', 'datetime',
        # relational like types
        'many2one', 'many2many', 'selection', 'tags',
        # UI types
        'separator',
    )

    def __init__(self, string: str | Sentinel = SENTINEL, **kwargs):
        super(Properties, self).__init__(string=string, **kwargs)

    def _setup_attrs(self, model_class, name):
        super()._setup_attrs(model_class, name)
        self._setup_definition_attrs()

    def _setup_definition_attrs(self):
        """ Setup the definition attributes. """
        # This is a simplified version - the full implementation would be much more complex
        pass

    def convert_to_cache(self, value, record, validate=True):
        if not value:
            return None
        # Properties are stored as JSON in the database
        return value

    def convert_to_record(self, value, record):
        return value if value else {}

    def convert_to_column(self, value, record, values=None, validate=True):
        if not value:
            return None
        # Import here to avoid circular imports
        from psycopg2.extras import Json as PsycopgJson
        return PsycopgJson(value)

    def convert_to_export(self, value, record):
        if not value:
            return ''
        import json
        return json.dumps(value)


class PropertiesDefinition(Field):
    """ Field used to define the properties definition (see :class:`~odoo.fields.Properties`
    field). This field is used on the container record to define the structure
    of expected properties on subrecords. It is used to check the properties
    definition. """
    type = 'properties_definition'
    _column_type = ('jsonb', 'jsonb')

    copy = False                        # properties definitions are not copied by default
    prefetch = False                    # not prefetched by default

    def convert_to_cache(self, value, record, validate=True):
        if not value:
            return None
        return value

    def convert_to_record(self, value, record):
        return value if value else []

    def convert_to_column(self, value, record, values=None, validate=True):
        if not value:
            return None
        # Import here to avoid circular imports
        from psycopg2.extras import Json as PsycopgJson
        return PsycopgJson(value)

    def convert_to_export(self, value, record):
        if not value:
            return ''
        import json
        return json.dumps(value)
