# Odoo Models.py Refactoring Plan

## Overview
The odoo/models.py file is 7,616 lines and contains multiple major concerns that need to be separated into modular components while maintaining 100% backward compatibility.

## Current Structure Analysis

### Major Components:
1. **MetaModel metaclass** (lines 230-295) - Model class creation logic
2. **BaseModel class** (lines 514-7393) - Core ORM functionality with 194+ methods
3. **RecordCache class** (lines 7394-7430) - Record caching functionality
4. **Model classes** (lines 7435+) - AbstractModel, Model, TransientModel
5. **Utility functions** - Various helpers throughout the file

### BaseModel Method Categories:

#### 1. Model Building & Setup (~50 methods)
- `_build_model`, `_build_model_check_base`, `_build_model_check_parent`
- `_setup_base`, `_setup_fields`, `_setup_complete`
- `_prepare_setup`, `_add_inherited_fields`, `_inherits_check`
- `_auto_init`, `init`, `_add_sql_constraints`

#### 2. CRUD Operations (~40 methods)
- `create`, `_create`, `_prepare_create_values`, `_add_precomputed_values`
- `read`, `_read_format`, `write`, `_write`, `_write_multi`
- `unlink`, `copy`, `copy_data`, `copy_translations`
- `exists`, `new`, `browse`

#### 3. Search & Query (~35 methods)
- `search`, `_search`, `search_count`, `search_fetch`, `search_read`
- `_where_calc`, `_apply_ir_rules`, `_order_to_sql`, `_order_field_to_sql`
- `read_group`, `_read_group`, `_read_group_select`, `_read_group_groupby`
- `name_search`, `name_create`, `_search_display_name`

#### 4. Cache Management (~25 methods)
- `invalidate_model`, `invalidate_recordset`, `_invalidate_cache`
- `flush_model`, `flush_recordset`, `_flush`, `_flush_search`
- `_recompute_model`, `_recompute_recordset`, `_recompute_field`
- `modified`, `_modified`, `_modified_triggers`

#### 5. Field Operations (~20 methods)
- `_fetch_field`, `fetch`, `_determine_fields_to_fetch`, `_fetch_query`
- `_field_to_sql`, `_condition_to_sql`, `_traverse_related_sql`
- `fields_get`, `check_field_access_rights`, `get_field_translations`

#### 6. Record Operations (~15 methods)
- Recordset manipulation: `__add__`, `__sub__`, `__and__`, `__or__`
- Iteration: `__iter__`, `__reversed__`, `__contains__`
- Comparison: `__eq__`, `__lt__`, `__le__`, `__gt__`, `__ge__`
- Access: `__getitem__`, `__setitem__`

#### 7. Access Control (~10 methods)
- `check_access`, `has_access`, `_filtered_access`, `_check_access`
- `check_access_rights`, `check_access_rule`, `_filter_access_rules`

## Proposed Modular Structure

### Phase 1: Extract Core Components

#### 1. `odoo/models/cache.py`
- Extract `RecordCache` class
- Cache-related methods from BaseModel:
  - `invalidate_model`, `invalidate_recordset`, `_invalidate_cache`
  - `flush_model`, `flush_recordset`, `_flush`, `_flush_search`
  - `_recompute_model`, `_recompute_recordset`, `_recompute_field`
  - `modified`, `_modified`, `_modified_triggers`
  - `_cache` property, `_in_cache_without`

#### 2. `odoo/models/fields.py`
- Field-related methods from BaseModel:
  - `_fetch_field`, `fetch`, `_determine_fields_to_fetch`, `_fetch_query`
  - `_field_to_sql`, `_condition_to_sql`, `_traverse_related_sql`
  - `fields_get`, `check_field_access_rights`
  - `get_field_translations`, `update_field_translations`
  - `_add_field`, `_pop_field`, `_valid_field_parameter`

#### 3. `odoo/models/crud.py`
- CRUD operation methods:
  - `create`, `_create`, `_prepare_create_values`, `_add_precomputed_values`
  - `read`, `_read_format`, `write`, `_write`, `_write_multi`
  - `unlink`, `copy`, `copy_data`, `copy_translations`
  - `exists`, `new`, `browse`

#### 4. `odoo/models/search.py`
- Search and query methods:
  - `search`, `_search`, `search_count`, `search_fetch`, `search_read`
  - `_where_calc`, `_apply_ir_rules`, `_order_to_sql`, `_order_field_to_sql`
  - `read_group`, `_read_group` and all related methods
  - `name_search`, `name_create`, `_search_display_name`

#### 5. `odoo/models/setup.py`
- Model building and setup methods:
  - `_build_model`, `_build_model_check_base`, `_build_model_check_parent`
  - `_setup_base`, `_setup_fields`, `_setup_complete`
  - `_prepare_setup`, `_add_inherited_fields`, `_inherits_check`
  - `_auto_init`, `init`, `_add_sql_constraints`

#### 6. `odoo/models/recordset.py`
- Recordset manipulation methods:
  - All magic methods: `__add__`, `__sub__`, `__and__`, `__or__`
  - Iteration: `__iter__`, `__reversed__`, `__contains__`
  - Comparison: `__eq__`, `__lt__`, `__le__`, `__gt__`, `__ge__`
  - Access: `__getitem__`, `__setitem__`
  - Utility: `mapped`, `filtered`, `sorted`, `grouped`

#### 7. `odoo/models/access.py`
- Access control methods:
  - `check_access`, `has_access`, `_filtered_access`, `_check_access`
  - `check_access_rights`, `check_access_rule`, `_filter_access_rules`
  - `_check_company`, `_check_company_domain`

#### 8. `odoo/models/base.py`
- Core BaseModel class with essential attributes and minimal methods
- Import and delegate to other modules
- Maintain the same public API

### Phase 2: Update Import Structure

#### 1. `odoo/models/__init__.py`
```python
# Maintain backward compatibility
from .base import BaseModel, AbstractModel
from .concrete import Model, TransientModel
from .cache import RecordCache

# Re-export everything to maintain existing imports
__all__ = ['BaseModel', 'AbstractModel', 'Model', 'TransientModel', 'RecordCache']
```

#### 2. Update `odoo/models.py`
- Keep as a compatibility layer that imports everything from the new modules
- Ensure all existing imports continue to work

## Implementation Strategy

### Critical Requirements:
1. **Zero Breaking Changes**: All existing imports must continue to work
2. **Identical Behavior**: Every method must work exactly as before
3. **Performance**: No performance degradation
4. **Testing**: Comprehensive testing after each module extraction

### Implementation Order:
1. Create new module structure
2. Extract RecordCache first (smallest, least dependencies)
3. Extract utility modules (cache, fields, access)
4. Extract operation modules (crud, search)
5. Extract setup module (most complex dependencies)
6. Create base module that ties everything together
7. Update __init__.py for backward compatibility
8. Comprehensive testing

### Backward Compatibility Strategy:
- Keep original odoo/models.py as a compatibility layer
- All new modules will be in odoo/models/ directory
- Use mixins and multiple inheritance to compose BaseModel
- Ensure all method signatures remain identical
- Maintain all class attributes and properties

## Testing Plan

### Import Testing:
1. Test all existing imports continue to work
2. Test that BaseModel has all expected methods
3. Test that method signatures are identical

### Functionality Testing:
1. Run existing test suite
2. Test CRUD operations
3. Test search and read_group operations
4. Test caching behavior
5. Test access control

### Performance Testing:
1. Benchmark before and after refactoring
2. Ensure no performance regression
3. Monitor memory usage

## Risk Mitigation

### High-Risk Areas:
1. Method resolution order (MRO) changes
2. Circular import dependencies
3. Dynamic method access patterns
4. Metaclass interactions

### Mitigation Strategies:
1. Careful MRO planning with mixins
2. Lazy imports where needed
3. Comprehensive testing of dynamic access
4. Preserve metaclass behavior exactly
