# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Model setup and building mixin for Odoo models.

This module contains model building and setup functionality extracted from the monolithic models.py file.
"""

import typing
from .. import api
from ..tools import OrderedSet, SQL

if typing.TYPE_CHECKING:
    from odoo.api import Self


class SetupMixin:
    """Mixin class containing model setup and building methods for BaseModel."""

    @classmethod
    def _build_model(cls, pool, cr):
        """ Instantiate a given model in the registry.

        This method creates or retrieves the model's class, determines its
        inheritance, and sets up its attributes and fields.
        """
        # determine model name
        if cls._name is None:
            name = cls.__name__.lower()
        else:
            name = cls._name

        # create or retrieve the model's class
        if name in pool:
            ModelClass = pool[name]
            ModelClass._build_model_check_base(cls)
        else:
            ModelClass = type(name, (cls,), {
                '_name': name,
                '_register': False,
                '_original_module': cls._module,
                '_inherit_module': {},
                '_inherit_children': OrderedSet(),
                '_inherits_children': set(),
                '_fields': {},
            })

        # determine inheritance
        parents = cls._inherit
        if isinstance(parents, str):
            parents = [parents]

        # build inheritance hierarchy
        bases = [ModelClass]
        for parent in parents:
            if parent in pool:
                parent_class = pool[parent]
                ModelClass._build_model_check_parent(parent_class)
                bases.append(parent_class)

        # set up the model class
        ModelClass.__bases__ = tuple(bases)
        ModelClass._build_model_attributes(pool)

        # link the class to the registry
        ModelClass.pool = pool
        pool[name] = ModelClass

        return ModelClass

    @classmethod
    def _build_model_check_base(model_class, cls):
        """ Check whether ``model_class`` can be extended with ``cls``. """
        if model_class._abstract and not cls._abstract:
            msg = ("%s inherits from abstract model %s but is not abstract itself. "
                   "You should make it abstract or inherit from a non-abstract model.")
            raise TypeError(msg % (cls, model_class))

    @classmethod
    def _build_model_check_parent(model_class, cls, parent_class):
        """ Check whether ``model_class`` can inherit from ``parent_class``. """
        if parent_class._abstract:
            return  # abstract models can always be inherited
        
        if model_class._name != parent_class._name:
            msg = "Model %s cannot inherit from %s because they have different names."
            raise TypeError(msg % (model_class._name, parent_class._name))

    @classmethod
    def _build_model_attributes(cls, pool):
        """ Initialize base model attributes. """
        # set default values for model attributes
        if not hasattr(cls, '_description'):
            cls._description = cls._name
        if not hasattr(cls, '_table'):
            cls._table = cls._name.replace('.', '_')
        if not hasattr(cls, '_log_access'):
            cls._log_access = True
        if not hasattr(cls, '_auto'):
            cls._auto = True
        if not hasattr(cls, '_order'):
            cls._order = 'id'

        # initialize constraint and onchange methods
        cls._init_constraints_onchanges()

    @classmethod
    def _init_constraints_onchanges(cls):
        """ Initialize constraint and onchange method lists. """
        # store list of sql constraint qualified names
        cls._sql_constraints = {}
        
        # collect constraint methods
        cls._constraint_methods = []
        for attr in dir(cls):
            method = getattr(cls, attr)
            if callable(method) and hasattr(method, '_constrains'):
                cls._constraint_methods.append(method)

        # collect onchange methods
        cls._onchange_methods = {}
        for attr in dir(cls):
            method = getattr(cls, attr)
            if callable(method) and hasattr(method, '_onchange'):
                for field_name in method._onchange:
                    if field_name not in cls._onchange_methods:
                        cls._onchange_methods[field_name] = []
                    cls._onchange_methods[field_name].append(method)

    @api.model
    def _prepare_setup(self):
        """ Prepare the setup of the model. """
        # set up inheritance
        cls = type(self)
        cls.__bases__ = cls.__base_classes

        # prepare field inheritance
        cls._field_inverses = {}
        cls._field_depends = {}

    @api.model
    def _setup_base(self):
        """ Determine the inherited and custom fields of the model. """
        cls = type(self)
        
        # collect fields from all base classes
        for base in reversed(cls.__mro__):
            if hasattr(base, '_fields'):
                for name, field in base._fields.items():
                    if name not in cls._fields:
                        cls._fields[name] = field

        # add inherited fields
        self._add_inherited_fields()

        # check _inherits consistency
        self._inherits_check()

    @api.model
    def _setup_fields(self):
        """ Setup the fields, except for recomputation triggers. """
        cls = type(self)
        
        # setup each field
        for name, field in cls._fields.items():
            field.setup_full(self)

        # setup field dependencies and inverses
        for name, field in cls._fields.items():
            # setup dependencies
            if field.depends:
                for dep in field.depends:
                    if dep not in cls._field_depends:
                        cls._field_depends[dep] = []
                    cls._field_depends[dep].append(field)

            # setup inverses
            if field.inverse:
                if field not in cls._field_inverses:
                    cls._field_inverses[field] = []
                cls._field_inverses[field].append(field.inverse)

    @api.model
    def _setup_complete(self):
        """ Setup recomputation triggers, and complete the model setup. """
        # setup field triggers for recomputation
        for field in self._fields.values():
            if field.compute:
                field.setup_triggers(self)

        # finalize model setup
        self._finalize_setup()

    def _finalize_setup(self):
        """ Finalize the model setup. """
        # add magic fields if needed
        if self._auto:
            self._add_magic_fields()

        # setup SQL constraints
        self._add_sql_constraints()

    def _add_magic_fields(self):
        """ Add magic fields like id, create_date, etc. """
        cls = type(self)
        
        # add id field if not present
        if 'id' not in cls._fields:
            from ..fields import Id
            cls._fields['id'] = Id()

        # add log access fields if enabled
        if self._log_access:
            if 'create_date' not in cls._fields:
                from ..fields import Datetime
                cls._fields['create_date'] = Datetime(string='Created on', readonly=True)
            if 'create_uid' not in cls._fields:
                from ..fields import Many2one
                cls._fields['create_uid'] = Many2one('res.users', string='Created by', readonly=True)
            if 'write_date' not in cls._fields:
                from ..fields import Datetime
                cls._fields['write_date'] = Datetime(string='Last Updated on', readonly=True)
            if 'write_uid' not in cls._fields:
                from ..fields import Many2one
                cls._fields['write_uid'] = Many2one('res.users', string='Last Updated by', readonly=True)

    @api.model
    def _add_inherited_fields(self):
        """ Determine inherited fields. """
        for parent_model, parent_field in self._inherits.items():
            parent = self.env[parent_model]
            for name, field in parent._fields.items():
                if name not in self._fields:
                    # inherit the field
                    self._fields[name] = field.copy()

    @api.model
    def _inherits_check(self):
        """ Check the _inherits consistency. """
        for table, field_name in self._inherits.items():
            field = self._fields.get(field_name)
            if not field:
                raise ValueError(f"Field {field_name!r} not found in model {self._name!r}")
            if field.type != 'many2one':
                raise ValueError(f"Field {field_name!r} must be Many2one for _inherits")
            if field.comodel_name != table:
                raise ValueError(f"Field {field_name!r} must point to model {table!r}")

    def _auto_init(self):
        """ Initialize the database schema of ``self``:
        - create the corresponding table,
        - create/update the necessary columns/tables for fields,
        - initialize new columns on existing rows,
        - add the SQL constraints given on the model,
        - add the indexes on indexed fields,

        Also prepare post-init stuff to:
        - add foreign key constraints,
        - reflect models, fields, relations and constraints,
        - mark fields to recompute on existing records.

        Note: you should not override this method. Instead, you can modify
        the model's database schema by overriding method :meth:`~.init`, which is
        called right after this one.
        """
        # create table if it doesn't exist
        cr = self.env.cr
        if not self._abstract and self._auto:
            cr.execute(f"""
                CREATE TABLE IF NOT EXISTS {self._table} (
                    id SERIAL PRIMARY KEY
                )
            """)

        # create/update columns for fields
        for name, field in self._fields.items():
            if field.store and field.column_type:
                self._init_column(name)

        # add SQL constraints
        self._add_sql_constraints()

    def _init_column(self, column_name):
        """ Initialize the value of the given column for existing rows. """
        field = self._fields[column_name]
        if not field.store:
            return

        cr = self.env.cr
        
        # check if column exists
        cr.execute("""
            SELECT column_name FROM information_schema.columns
            WHERE table_name = %s AND column_name = %s
        """, [self._table, column_name])
        
        if not cr.fetchone():
            # create the column
            column_def = field.column_type[1]
            if field.required:
                column_def += " NOT NULL"
            
            cr.execute(f"ALTER TABLE {self._table} ADD COLUMN {column_name} {column_def}")

    def _add_sql_constraints(self):
        """ Modify this model's database table constraints so they match the one
        in _sql_constraints.
        """
        if not self._auto:
            return

        cr = self.env.cr
        for name, (constraint_name, definition, message) in self._sql_constraints.items():
            # check if constraint exists
            cr.execute("""
                SELECT constraint_name FROM information_schema.table_constraints
                WHERE table_name = %s AND constraint_name = %s
            """, [self._table, constraint_name])
            
            if not cr.fetchone():
                # create the constraint
                cr.execute(f"ALTER TABLE {self._table} ADD CONSTRAINT {constraint_name} {definition}")

    @api.private
    def init(self):
        """ This method is called after :meth:`~._auto_init`, and may be
        overridden to create additional database structures for this model.
        """
        pass

    def _register_hook(self):
        """ stuff to do right after the registry is built """
        pass

    def _unregister_hook(self):
        """ Clean up what `~._register_hook` has done. """
        pass
