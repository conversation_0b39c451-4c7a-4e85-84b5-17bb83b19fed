﻿# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Backward compatibility layer for Odoo fields.

This file maintains backward compatibility with the original monolithic fields.py
while delegating to the new modular structure in odoo/fields/ package.

IMPORTANT: This file should not be modified directly. All field functionality
has been moved to the odoo/fields/ package for better maintainability.
"""

from __future__ import annotations

# Import everything from the modular fields package to maintain backward compatibility
from .fields import *

# Re-export all symbols for backward compatibility
# This ensures that all existing imports like "from odoo.fields import Field" continue to work
__all__ = getattr(__import__('odoo.fields'), '__all__', [])
