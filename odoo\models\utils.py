# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Utility functions for Odoo models.

This module contains utility functions that are used by both models and fields
to avoid circular import issues.
"""

import re
from ..exceptions import ValidationError
from ..api import NewId

# Regex patterns
regex_alphanumeric = re.compile(r'^[a-z0-9_]+$')
regex_pg_name = re.compile(r'^[a-z_][a-z0-9_$]*$', re.I)


def check_property_field_value_name(property_name):
    """Check if a property field value name is valid."""
    if not regex_alphanumeric.match(property_name) or len(property_name) > 512:
        raise ValueError(f"Wrong property field value name {property_name!r}.")


def check_pg_name(name):
    """ Check whether the given name is a valid PostgreSQL identifier name. """
    if not regex_pg_name.match(name):
        raise ValidationError("Invalid characters in table name %r" % name)
    if len(name) > 63:
        raise ValidationError("Table name %r is too long" % name)


def expand_ids(id0, ids):
    """ Return an iterator of unique ids from the concatenation of ``[id0]`` and
    ``ids``, and of the same kind (all origin ids or all new ids).
    """
    yield id0
    seen = {id0}
    for id in ids:
        if id not in seen:
            yield id
            seen.add(id)


def is_definition_class(cls):
    """ Return whether ``cls`` is a model definition class. """
    return isinstance(cls, type) and hasattr(cls, '_name')


# Constants
PREFETCH_MAX = 1000

READ_GROUP_NUMBER_GRANULARITY = {
    'day': 'day',
    'week': 'week',
    'month': 'month',
    'quarter': 'quarter',
    'year': 'year',
}
