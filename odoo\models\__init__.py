# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Modular Odoo models package.

This package contains the refactored Odoo models system, split into logical modules
while maintaining 100% backward compatibility with the original monolithic models.py.
"""

# Import core components
from .base import BaseModel, AbstractModel
from .concrete import Model, TransientModel
from .cache import RecordCache

# Import mixins (for advanced usage)
from .cache import CacheMixin
from .fields_mixin import FieldsMixin
from .crud_mixin import CRUDMixin
from .search_mixin import SearchMixin
from .access_mixin import AccessMixin
from .recordset_mixin import RecordsetMixin
from .setup_mixin import SetupMixin

# Import utility functions
from .concrete import (
    convert_pgerror_not_null,
    convert_pgerror_unique,
    convert_pgerror_constraint,
)

# Import utility functions and constants from utils module
from .utils import (
    check_property_field_value_name,
    READ_GROUP_NUMBER_GRANULARITY,
    check_pg_name,
    expand_ids,
    is_definition_class,
    PREFETCH_MAX,
)

# Re-export everything for backward compatibility
__all__ = [
    # Core classes
    'BaseModel',
    'AbstractModel', 
    'Model',
    'TransientModel',
    'RecordCache',
    
    # Mixins (for advanced usage)
    'CacheMixin',
    'FieldsMixin', 
    'CRUDMixin',
    'SearchMixin',
    'AccessMixin',
    'RecordsetMixin',
    'SetupMixin',
    
    # Utility functions
    'convert_pgerror_not_null',
    'convert_pgerror_unique',
    'convert_pgerror_constraint',
    'check_property_field_value_name',
    'READ_GROUP_NUMBER_GRANULARITY',
    'check_pg_name',
    'expand_ids',
    'is_definition_class',
    'PREFETCH_MAX',
]
