# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

""" Relational field types: Many2one, One2many, Many2many, and related classes. """
from __future__ import annotations

import typing
import logging
import itertools
from operator import attrgetter
from collections import defaultdict

from .base import Field
from .basic import Integer
from ..tools.misc import unquote, Sentinel, SENTINEL
from ..tools.sql import pg_varchar
from ..tools import OrderedSet, sql, SQL

from odoo.api import ContextType, DomainType, M, NewId

_logger = logging.getLogger(__name__)


class _Relational(Field[M], typing.Generic[M]):
    """ Abstract class for relational fields. """
    relational = True
    domain: DomainType = []         # domain for searching values
    context: ContextType = {}       # context for searching values
    check_company = False

    def __get__(self, records, owner=None):
        # base case: do the regular access
        if records is None or len(records._ids) <= 1:
            return super().__get__(records, owner)
        # multirecord case: use mapped
        return self.mapped(records)

    def setup_nonrelated(self, model):
        super().setup_nonrelated(model)
        if self.comodel_name not in model.pool:
            _logger.warning("Field %s with unknown comodel_name %r", self, self.comodel_name)
            self.comodel_name = '_unknown'

    def get_domain_list(self, model):
        """ Return a list domain from the domain parameter. """
        domain = self.domain
        if callable(domain):
            domain = domain(model)
        return domain if isinstance(domain, list) else []

    @property
    def _related_domain(self):
        def validated(domain):
            if isinstance(domain, str) and not self.inherited:
                # string domains are expressions that are not valid for self's model
                return None
            return domain

        if callable(self.domain):
            # will be called with another model than self's
            return lambda recs: validated(self.domain(recs.env[self.model_name]))  # pylint: disable=not-callable
        else:
            return validated(self.domain)

    _related_context = property(attrgetter('context'))

    _description_relation = property(attrgetter('comodel_name'))
    _description_context = property(attrgetter('context'))

    def _description_domain(self, env):
        domain = self.domain(env[self.model_name]) if callable(self.domain) else self.domain  # pylint: disable=not-callable
        if self.check_company:
            field_to_check = None
            if self.company_dependent:
                cids = '[allowed_company_ids[0]]'
            elif self.model_name == 'res.company':
                # when using check_company=True on a field on 'res.company', the
                # company_id comes from the id of the current record
                cids = '[id]'
            elif 'company_id' in env[self.model_name]:
                cids = '[company_id]'
                field_to_check = 'company_id'
            elif 'company_ids' in env[self.model_name]:
                cids = 'company_ids'
                field_to_check = 'company_ids'
            else:
                _logger.warning(env._(
                    "Couldn't generate a company-dependent domain for field %s. "
                    "The model doesn't have a 'company_id' or 'company_ids' field, and isn't company-dependent either.",
                    self.model_name + '.' + self.name,
                ))
                return domain
            company_domain = env[self.comodel_name]._check_company_domain(companies=unquote(cids))
            if not field_to_check:
                return f"{company_domain} + {domain or []}"
            else:
                no_company_domain = env[self.comodel_name]._check_company_domain(companies='')
                return f"({field_to_check} and {company_domain} or {no_company_domain}) + ({domain or []})"
        return domain

    def _description_allow_hierachy_operators(self, env):
        """ Return if the child_of/parent_of makes sense on this field """
        comodel = env[self.comodel_name]
        return comodel._parent_name in comodel._fields


class Many2one(_Relational[M]):
    """ The value of such a field is a recordset of size 0 (no
    record) or 1 (a single record).

    :param str comodel_name: name of the target model
        ``Mandatory`` except for related or extended fields.

    :param domain: an optional domain to set on candidate values on the
        client side (domain or a python expression that will be evaluated
        to provide domain)

    :param dict context: an optional context to use on the client side when
        handling that field

    :param str ondelete: what to do when the referred record is deleted;
        possible values are: ``'set null'``, ``'restrict'``, ``'cascade'``

    :param bool auto_join: whether JOINs are generated upon search through that
        field (default: ``False``)

    :param bool delegate: set it to ``True`` to make fields of the target model
        accessible from the current model (corresponds to ``_inherits``)

    :param bool check_company: Mark the field to be verified in
        :meth:`~odoo.models.Model._check_company`. Add a default company
        domain depending on the field attributes.
    """
    type = 'many2one'
    _column_type = ('int4', 'int4')

    ondelete = 'set null'               # what to do when value is deleted
    auto_join = False                   # whether joins are generated upon search
    delegate = False                    # whether self implements delegation

    def __init__(self, comodel_name=SENTINEL, string: str | Sentinel = SENTINEL, **kwargs):
        super(Many2one, self).__init__(comodel_name=comodel_name, string=string, **kwargs)

    def _get_attrs(self, model_class, name):
        attrs = super()._get_attrs(model_class, name)
        # determine self.delegate
        if attrs.get('delegate'):
            attrs['ondelete'] = 'cascade'
        return attrs

    def setup_nonrelated(self, model):
        super().setup_nonrelated(model)
        # 3 cases:
        # 1) The ondelete attribute is already defined, we keep it
        # 2) The ondelete attribute is not defined, or is set to its
        #    default value, and self is not required: we set it to 'set null'
        # 3) The ondelete attribute is not defined, or is set to its
        #    default value, and self is required: we set it to 'cascade'
        if self.ondelete == 'set null' and self.required:
            self.ondelete = 'cascade'

    def update_db(self, model, columns):
        comodel = model.env[self.comodel_name]
        if not comodel._abstract:
            model._cr.execute(
                "SELECT conname FROM pg_constraint WHERE contype='f' AND conrelid=%s AND conname=%s",
                (model._table_query, self._get_constraint_name()),
            )
            if not model._cr.rowcount:
                model._cr.execute(
                    "ALTER TABLE %s ADD CONSTRAINT %s FOREIGN KEY (%s) REFERENCES %s(id) ON DELETE %s",
                    (
                        model._table_query,
                        self._get_constraint_name(),
                        self.name,
                        comodel._table,
                        self.ondelete.upper().replace(' ', ' '),
                    ),
                )
        super().update_db(model, columns)

    def _get_constraint_name(self):
        return f"{self.model_name.replace('.', '_')}_{self.name}_fkey"

    def convert_to_column(self, value, record, values=None, validate=True):
        return self.convert_to_cache(value, record, validate)

    def convert_to_cache(self, value, record, validate=True):
        # Import here to avoid circular imports
        from ..models import BaseModel
        from odoo.api import NewId
        
        # cache format: id or None
        if type(value) is int or type(value) is NewId:
            id_ = value
        elif isinstance(value, BaseModel):
            if validate and (value._name != self.comodel_name or len(value) > 1):
                raise ValueError("Wrong value for %s: %r" % (self, value))
            id_ = value._ids[0] if value._ids else None
        elif isinstance(value, tuple):
            # value is either a pair (id, name), or a tuple of ids
            id_ = value[0] if value else None
        elif isinstance(value, dict):
            # return the id corresponding to value, or None
            id_ = value.get('id')
        else:
            id_ = None
        if validate and id_:
            prefetch_ids = record._prefetch_ids
            if id_ not in prefetch_ids:
                # make sure the record exists
                comodel = record.env[self.comodel_name]
                if not comodel.browse(id_).exists():
                    if record.env.context.get('import_file'):
                        # we are importing data, give a chance to resolve the reference later
                        return id_
                    raise ValueError("Wrong value for %s: %r" % (self, value))
        return id_

    def convert_to_record(self, value, record):
        return record.env[self.comodel_name].browse(value) if value else record.env[self.comodel_name]

    def convert_to_read(self, value, record, use_display_name=True):
        if not value:
            return False
        if isinstance(value, tuple):
            # value is either a pair (id, name), or a tuple of ids
            return value
        if isinstance(value, dict):
            return value
        if use_display_name:
            # return a pair (id, display_name)
            return (value.id, value.display_name)
        else:
            return value.id

    def convert_to_write(self, value, record):
        # Import here to avoid circular imports
        from ..models import BaseModel
        
        if not value:
            return False
        if isinstance(value, BaseModel) and value._name == self.comodel_name:
            return value.id
        if isinstance(value, tuple):
            # value is either a pair (id, name), or a tuple of ids
            return value[0] if value else False
        if isinstance(value, dict):
            return value.get('id', False)
        return int(value)

    def convert_to_export(self, value, record):
        return value.display_name if value else ''

    def convert_to_display_name(self, value, record):
        return value.display_name if value else False


class Many2oneReference(Integer):
    """ Pseudo-relational field (no FK in database).

    The field value is stored as an :class:`integer <int>` id in database.

    Contrary to :class:`Reference` fields, the model has to be specified
    in a :class:`Char` field whose name has to be specified in the
    `model_field` attribute for this field.

    :param str model_field: name of the field that contains the model name
    """
    type = 'many2one_reference'

    model_field = None

    def __init__(self, model_field=SENTINEL, string: str | Sentinel = SENTINEL, **kwargs):
        super(Many2oneReference, self).__init__(model_field=model_field, string=string, **kwargs)

    def convert_to_cache(self, value, record, validate=True):
        # cache format: id or None
        # Import here to avoid circular imports
        from ..models import BaseModel
        
        if isinstance(value, BaseModel):
            value = value._ids[0] if value._ids else None
        return super().convert_to_cache(value, record, validate)

    def convert_to_record(self, value, record):
        if not (value and record[self.model_field]):
            return record.env['']
        return record.env[record[self.model_field]].browse(value)

    def convert_to_read(self, value, record, use_display_name=True):
        if not (value and record[self.model_field]):
            return False
        return self.convert_to_record(value, record).name_get()[0]

    def convert_to_export(self, value, record):
        if not (value and record[self.model_field]):
            return ''
        return self.convert_to_record(value, record).display_name


class _RelationalMulti(_Relational[M], typing.Generic[M]):
    r"Abstract class for relational fields \*2many."
    write_sequence = 20

    # Important: the cache contains the ids of all the records in the relation,
    # including inactive records.  Inactive records are filtered out by
    # convert_to_record(), depending on the context.

    def convert_to_cache(self, value, record, validate=True):
        # cache format: tuple(ids)
        # Import here to avoid circular imports
        from ..models import BaseModel

        if isinstance(value, BaseModel):
            if validate and value._name != self.comodel_name:
                raise ValueError("Wrong value for %s: %s" % (self, value))
            ids = value._ids
        elif isinstance(value, list):
            ids = tuple(value)
        elif isinstance(value, tuple):
            ids = value
        else:
            ids = ()
        return ids

    def convert_to_record(self, value, record):
        # return the recordset, depending on the context
        comodel = record.env[self.comodel_name]
        if isinstance(value, tuple):
            # a tuple of ids, this is the cache format
            value = record.env[self.comodel_name].browse(value)

        # Import here to avoid circular imports
        from ..models import BaseModel

        if isinstance(value, BaseModel) and value._name == self.comodel_name:
            def get_origin(val):
                return val._origin if isinstance(val, BaseModel) else val

            # make result with new and existing records
            inv_names = {field.name for field in record.pool.field_inverses[self]}
            if inv_names:
                # make sure self's inverse fields are in value's cache
                missing_ids = record._prefetch_ids - set(value._cache)
                if missing_ids:
                    # determine what to prefetch based on value's cache content
                    ids = [
                        id_ for id_ in record._prefetch_ids
                        if not any(record._cache.get_missing_ids(record.browse([id_]), field)
                                   for field in inv_names)
                    ]
                    if ids:
                        value.browse(ids).fetch(inv_names)

            # return result ordered as value, without duplicates
            return comodel.browse(dict.fromkeys(get_origin(val).id for val in value))

        return comodel.browse(value._ids if value else ())

    def convert_to_read(self, value, record, use_display_name=True):
        return value.ids

    def convert_to_write(self, value, record):
        # Import here to avoid circular imports
        from ..models import BaseModel

        if isinstance(value, BaseModel) and value._name == self.comodel_name:
            return value.ids
        elif isinstance(value, (list, tuple)):
            return list(value)
        else:
            return []

    def create(self, record_values):
        """ Write the value of ``self`` on the given records, which have just
        been created.

        :param record_values: a list of pairs ``(record, value)``, where
            ``value`` is in the format of method :meth:`BaseModel.write`
        """
        self.write_batch(record_values, True)

    def write_batch(self, records_commands_list, create=False):
        """ Update the cached values of ``self`` for several records. """
        # Import here to avoid circular imports
        from ..models import BaseModel

        # Import Command here to avoid circular imports
        from .properties import Command

        for idx, (recs, value) in enumerate(records_commands_list):
            if isinstance(value, tuple):
                value = [Command.set(value)]
            elif isinstance(value, BaseModel) and value._name == self.comodel_name:
                value = [Command.set(value._ids)]
            elif value is False or value is None:
                value = [Command.clear()]
            elif not isinstance(value, list):
                continue

            records_commands_list[idx] = (recs, value)

        # determine the corecords of each command, and prepare the updates on
        # the inverse fields
        updates = []                    # [(records, field, value)]

        for records, commands in records_commands_list:
            for command in commands:
                if not isinstance(command, Command):
                    continue
                corecords = records.env[self.comodel_name].browse(command.ids)
                if command.create:
                    updates.append((corecords, self.inverse_name, records))
                elif command.delete:
                    updates.append((corecords, self.inverse_name, records.env[self.model_name]))
                elif command.link:
                    updates.append((corecords, self.inverse_name, records))
                elif command.unlink:
                    updates.append((corecords, self.inverse_name, records.env[self.model_name]))

        # update the cache of records
        cache = records.env.cache
        for records, commands in records_commands_list:
            cache_value = self.convert_to_cache(commands, records)
            cache.update(records, self, itertools.repeat(cache_value))

        # update the cache of corecords (inverse fields)
        for corecords, field, value in updates:
            cache.update(corecords, field, itertools.repeat(value))


class One2many(_RelationalMulti[M]):
    """One2many field; the value of such a field is the recordset of all the
    records in ``comodel_name`` such that the field ``inverse_name`` is equal to
    the current record.

    :param str comodel_name: name of the target model

    :param str inverse_name: name of the inverse ``many2one`` field in
        ``comodel_name``

    :param domain: an optional domain to set on candidate values on the
        client side (domain or a python expression that will be evaluated
        to provide domain)

    :param dict context: an optional context to use on the client side when
        handling that field

    :param bool auto_join: whether JOINs are generated upon search through that
        field (default: ``False``)

    :param bool copy: whether the field value should be copied when the record
        is duplicated (default: ``False``)

    The attributes ``comodel_name`` and ``inverse_name`` are mandatory except in
    the case of related fields or field extensions.
    """
    type = 'one2many'

    inverse_name = None                 # name of the inverse field
    auto_join = False                   # whether joins are generated upon search
    copy = False                        # o2m are not copied by default

    def __init__(self, comodel_name=SENTINEL, inverse_name=SENTINEL, string: str | Sentinel = SENTINEL, **kwargs):
        super(One2many, self).__init__(
            comodel_name=comodel_name,
            inverse_name=inverse_name,
            string=string,
            **kwargs
        )

    def _get_attrs(self, model_class, name):
        attrs = super()._get_attrs(model_class, name)
        if not attrs.get('inverse_name'):
            attrs['inverse_name'] = model_class._name.replace('.', '_') + '_id'
        return attrs

    def setup_nonrelated(self, model):
        super().setup_nonrelated(model)
        if self.inverse_name:
            # link self to its inverse field and vice-versa
            comodel = model.env[self.comodel_name]
            inverse_field = comodel._fields.get(self.inverse_name)
            if inverse_field:
                # set inverse_field on self, and self on inverse_field
                self.inverse_field = inverse_field
                if isinstance(inverse_field, Many2one):
                    inverse_field.inverse_field = self
                    # one2many fields are not copied by default, but the
                    # inverse many2one field may force copy=True; in that
                    # case, we should copy the one2many field, too
                    if inverse_field.copy:
                        self.copy = True

    def setup_related(self, model):
        super().setup_related(model)
        # self.inverse_name may be overridden by _get_attrs()
        if self.inverse_name:
            field = self.related_field
            self.inverse_name = field.inverse_name

    _related_inverse_name = property(attrgetter('inverse_name'))
    _description_relation_field = property(attrgetter('inverse_name'))

    def update_db(self, model, columns):
        if self.inverse_name:
            # add the foreign key constraint on the inverse field
            comodel = model.env[self.comodel_name]
            if not comodel._abstract and self.inverse_name in comodel._fields:
                inverse_field = comodel._fields[self.inverse_name]
                inverse_field.update_db(comodel, columns)
        super().update_db(model, columns)


class Many2many(_RelationalMulti[M]):
    """ Many2many field; the value of such a field is the recordset.

    :param comodel_name: name of the target model (string)
        mandatory except in the case of related or extended fields

    :param str relation: optional name of the table that stores the relation in
        the database

    :param str column1: optional name of the column referring to "these" records
        in the table ``relation``

    :param str column2: optional name of the column referring to "those" records
        in the table ``relation``

    The attributes ``relation``, ``column1`` and ``column2`` are optional.
    If not given, names are automatically generated from model names,
    provided ``model_name`` and ``comodel_name`` are different!

    Note that having several fields with implicit relation parameters on a
    given model with the same comodel is not accepted by the ORM, since
    those field would use the same table. The ORM prevents two many2many
    fields to use the same relation parameters, except if

    - both fields use the same model, comodel, and relation parameters are
      explicit; or

    - at least one field belongs to a model with ``_auto = False``.

    :param domain: an optional domain to set on candidate values on the
        client side (domain or a python expression that will be evaluated
        to provide domain)

    :param dict context: an optional context to use on the client side when
        handling that field

    :param bool check_company: Mark the field to be verified in
        :meth:`~odoo.models.Model._check_company`. Add a default company
        domain depending on the field attributes.

    """
    type = 'many2many'

    _explicit = True                    # whether schema is explicitly given
    relation = None                     # name of table
    column1 = None                      # column of table referring to model
    column2 = None                      # column of table referring to comodel
    auto_join = False                   # whether joins are generated upon search
    ondelete = 'cascade'                # optional ondelete for the column2 fkey

    def __init__(self, comodel_name: str | Sentinel = SENTINEL, relation: str | Sentinel = SENTINEL,
                 column1: str | Sentinel = SENTINEL, column2: str | Sentinel = SENTINEL,
                 string: str | Sentinel = SENTINEL, **kwargs):
        super(Many2many, self).__init__(
            comodel_name=comodel_name,
            relation=relation,
            column1=column1,
            column2=column2,
            string=string,
            **kwargs
        )

    def setup_nonrelated(self, model):
        super().setup_nonrelated(model)
        # Basic setup - full implementation would be much more complex
        if self.ondelete not in ('cascade', 'restrict'):
            raise ValueError(
                "The m2m field %s of model %s declares its ondelete policy "
                "as being %r. Only 'restrict' and 'cascade' make sense."
                % (self.name, model._name, self.ondelete)
            )

    def read(self, records):
        # Simplified read method - full implementation would be much more complex
        context = {'active_test': False}
        context.update(self.context)
        comodel = records.env[self.comodel_name].with_context(**context)

        # For now, return empty tuples for all records
        values = [() for _ in records._ids]
        records.env.cache.insert_missing(records, self, values)

    def write_real(self, records_commands_list, create=False):
        # Simplified write method - full implementation would be much more complex
        pass

    def write_new(self, records_commands_list):
        # Simplified write method for new records
        pass


class PrefetchMany2one:
    """ Iterable for the values of a many2one field on the prefetch set of a given record. """
    __slots__ = 'record', 'field'

    def __init__(self, record, field):
        self.record = record
        self.field = field

    def __iter__(self):
        records = self.record.browse(self.record._prefetch_ids)
        return iter(records.mapped(self.field.name))


class PrefetchX2many:
    """ Iterable for the values of an x2many field on the prefetch set of a given record. """
    __slots__ = 'record', 'field'

    def __init__(self, record, field):
        self.record = record
        self.field = field

    def __iter__(self):
        records = self.record.browse(self.record._prefetch_ids)
        for record in records:
            yield record[self.field.name]
