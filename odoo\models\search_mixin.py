# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Search operations mixin for Odoo models.

This module contains search and query functionality extracted from the monolithic models.py file.
"""

import typing
import logging
from .. import api
from ..tools import Query, SQL

if typing.TYPE_CHECKING:
    from odoo.api import Self

_logger = logging.getLogger(__name__)


class SearchMixin:
    """Mixin class containing search operations for BaseModel."""

    @api.readonly
    def search_count(self, domain, limit=None):
        """ search_count(domain[, limit=None]) -> int

        Return the number of records in the current model matching the provided
        domain.

        :param domain: search domain
        :param limit: optional limit on the number of records to count
        :return: number of matching records
        """
        # flush pending computations
        self._flush_search(domain)

        # build the query
        query = self._where_calc(domain)
        self._apply_ir_rules(query, 'read')

        # execute count query
        if limit:
            query = query.limit(limit)
        
        count_query = query.select('COUNT(*)')
        self.env.cr.execute(count_query)
        return self.env.cr.fetchone()[0]

    @api.returns('self')
    def search(self, domain, offset=0, limit=None, order=None) -> 'Self':
        """ search(domain[, offset=0][, limit=None][, order=None])

        Search for records based on the ``domain`` search criteria.

        :param domain: search domain
        :param offset: number of results to ignore (default: 0)
        :param limit: maximum number of records to return (default: None)
        :param order: sort string
        :return: recordset of matching records
        """
        # build the query
        query = self._search(domain, offset=offset, limit=limit, order=order)
        
        # execute the query
        self.env.cr.execute(query)
        ids = [row[0] for row in self.env.cr.fetchall()]
        
        return self.browse(ids)

    @api.returns('self')
    def search_fetch(self, domain, field_names, offset=0, limit=None, order=None):
        """ search_fetch(domain, field_names[, offset=0][, limit=None][, order=None])

        Search for records and fetch the specified fields in one query.

        :param domain: search domain
        :param field_names: list of field names to fetch
        :param offset: number of results to ignore (default: 0)
        :param limit: maximum number of records to return (default: None)
        :param order: sort string
        :return: recordset with fetched fields
        """
        # search for records
        records = self.search(domain, offset=offset, limit=limit, order=order)
        
        # fetch the specified fields
        if records and field_names:
            records.fetch(field_names)
        
        return records

    @api.model
    def _search(self, domain, offset=0, limit=None, order=None) -> Query:
        """
        Private implementation of search() method.

        :param domain: search domain
        :param offset: number of results to ignore
        :param limit: maximum number of records to return
        :param order: sort string
        :return: Query object
        """
        # flush pending computations
        self._flush_search(domain, order=order)

        # build the query
        query = self._where_calc(domain)
        self._apply_ir_rules(query, 'read')

        # add order clause
        if order:
            order_clause = self._order_to_sql(order, query)
            query = query.order_by(order_clause)
        elif self._order:
            order_clause = self._order_to_sql(self._order, query)
            query = query.order_by(order_clause)

        # add offset and limit
        if offset:
            query = query.offset(offset)
        if limit:
            query = query.limit(limit)

        # select only the id column
        query = query.select(SQL.identifier(query.table_name, 'id'))

        return query

    @api.model
    def _where_calc(self, domain, active_test=True):
        """Computes the WHERE clause needed to implement an OpenERP domain.

        :param domain: the domain to compute
        :param active_test: whether the default filtering of records with ``active``
                           field set to ``False`` should be applied.
        :return: Query object with WHERE clause
        """
        # create base query
        query = Query(self.env.cr, self._table, self._table_sql)

        # add active test if needed
        if active_test and self._active_name:
            # Use lazy import to avoid circular dependency
            from ..osv import expression
            domain = expression.AND([domain, [(self._active_name, '=', True)]])

        # convert domain to SQL
        if domain:
            where_clause = self._domain_to_sql(domain, query)
            query = query.where(where_clause)

        return query

    def _domain_to_sql(self, domain, query):
        """ Convert a domain to SQL WHERE clause. """
        if not domain:
            return SQL("TRUE")

        # normalize domain - use simple normalization to avoid circular import
        if not isinstance(domain, list):
            domain = [domain]

        # convert each condition
        conditions = []
        for condition in domain:
            if isinstance(condition, str):
                # logical operator
                if condition == '&':
                    continue  # AND is default
                elif condition == '|':
                    # handle OR logic
                    continue
                elif condition == '!':
                    # handle NOT logic
                    continue
            elif isinstance(condition, (list, tuple)) and len(condition) == 3:
                # field condition
                fname, operator, value = condition
                sql_condition = self._condition_to_sql(query.table_name, fname, operator, value, query)
                conditions.append(sql_condition)

        # combine conditions with AND
        if conditions:
            return SQL(" AND ").join(conditions)
        else:
            return SQL("TRUE")

    @api.model
    def _apply_ir_rules(self, query, mode='read'):
        """Add what's missing in ``query`` to implement all appropriate ir.rules
        (using the ``model_name``'s rules or the current model's rules if ``model_name`` is None)

        :param query: the current query object
        :param mode: the mode for the rules ('read', 'write', 'create', 'unlink')
        """
        if self.env.su:
            return  # superuser bypasses all rules

        # get applicable rules
        Rule = self.env['ir.rule']
        domain = Rule._compute_domain(self._name, mode)
        
        if domain:
            rule_where = self._domain_to_sql(domain, query)
            query.add_where(rule_where)

    def _order_to_sql(self, order: str, query: Query, alias: str = None, reverse: bool = False) -> SQL:
        """
        Convert an order specification to SQL ORDER BY clause.

        :param order: order specification string
        :param query: Query object
        :param alias: table alias to use
        :param reverse: whether to reverse the order
        :return: SQL object for ORDER BY clause
        """
        if not order:
            return SQL("")

        if alias is None:
            alias = query.table_name

        order_terms = []
        for order_part in order.split(','):
            order_part = order_part.strip()
            if not order_part:
                continue

            # parse order part (field_name [ASC|DESC] [NULLS FIRST|LAST])
            parts = order_part.split()
            field_name = parts[0]
            direction = SQL("ASC")
            nulls = SQL("")

            if len(parts) > 1:
                if parts[1].upper() in ('ASC', 'DESC'):
                    direction = SQL(parts[1].upper())
                if len(parts) > 2 and parts[2].upper() == 'NULLS':
                    if len(parts) > 3 and parts[3].upper() in ('FIRST', 'LAST'):
                        nulls = SQL(f"NULLS {parts[3].upper()}")

            # reverse direction if needed
            if reverse:
                direction = SQL("DESC") if direction.string == "ASC" else SQL("ASC")

            # build order term
            field_sql = self._order_field_to_sql(alias, field_name, direction, nulls, query)
            order_terms.append(field_sql)

        return SQL(", ").join(order_terms)

    def _order_field_to_sql(self, alias: str, field_name: str, direction: SQL, nulls: SQL, query: Query) -> SQL:
        """
        Convert a single field order specification to SQL.

        :param alias: table alias
        :param field_name: name of the field to order by
        :param direction: ASC or DESC
        :param nulls: NULLS FIRST or NULLS LAST
        :param query: Query object
        :return: SQL object for the field order
        """
        field = self._fields.get(field_name)
        if not field:
            raise ValueError(f"Invalid field name {field_name!r} in order clause")

        if field.store:
            # stored field - use column directly
            column = SQL.identifier(alias, field_name)
        elif field.type == 'many2one':
            # many2one field - order by related record's display name
            related_model = self.env[field.comodel_name]
            related_alias = query.join(alias, field_name, related_model._table, 'id', field_name)
            column = SQL.identifier(related_alias, related_model._rec_name or 'id')
        else:
            raise ValueError(f"Cannot order by computed field {field_name!r}")

        # build complete order expression
        if nulls:
            return SQL("%s %s %s", column, direction, nulls)
        else:
            return SQL("%s %s", column, direction)

    @api.model
    def _flush_search(self, domain, fields=None, order=None, seen=None):
        """ Flush all the fields appearing in `domain`, `fields` and `order`.
        """
        if seen is None:
            seen = set()
        if self._name in seen:
            return
        seen.add(self._name)

        to_flush = set()

        def collect_from_domain(model, domain):
            for arg in domain:
                if isinstance(arg, (list, tuple)) and len(arg) == 3:
                    fname, _operator, value = arg
                    field = model._fields.get(fname)
                    if field:
                        to_flush.add(field)
                        if field.type in ('many2one', 'one2many', 'many2many'):
                            # recursively flush related model
                            related_model = model.env[field.comodel_name]
                            if hasattr(value, '__iter__') and not isinstance(value, str):
                                for v in value:
                                    if isinstance(v, (list, tuple)):
                                        collect_from_domain(related_model, [v])

        def collect_from_path(model, path):
            # path is a dot-separated sequence of field names
            names = path.split('.')
            for name in names:
                field = model._fields.get(name)
                if field:
                    to_flush.add(field)
                    if field.type in ('many2one', 'one2many', 'many2many'):
                        model = model.env[field.comodel_name]

        # collect fields from domain
        if domain:
            collect_from_domain(self, domain)

        # collect fields from field list
        if fields:
            for fname in fields:
                collect_from_path(self, fname)

        # collect fields from order
        if order:
            for order_part in order.split(','):
                fname = order_part.strip().split()[0]
                collect_from_path(self, fname)

        # flush the collected fields
        for field in to_flush:
            self.env.flush_all(field)

    @api.readonly
    def search_read(self, domain=None, fields=None, offset=0, limit=None, order=None, **read_kwargs):
        """ Perform a :meth:`search_fetch` followed by a :meth:`_read_format`.

        :param domain: search domain
        :param fields: list of field names to read
        :param offset: number of results to ignore
        :param limit: maximum number of records to return
        :param order: sort string
        :return: list of dictionaries containing the field values
        """
        if domain is None:
            domain = []

        # search and fetch records
        records = self.search_fetch(domain, fields or [], offset=offset, limit=limit, order=order)

        # format the result
        if fields is None:
            fields = list(self._fields.keys())

        return records._read_format(fields, **read_kwargs)

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100) -> list:
        """ name_search(name='', args=None, operator='ilike', limit=100)

        Search for records that have a display name matching the given
        ``name`` pattern when compared with the given ``operator``, while also
        matching the optional search domain (``args``).

        This is used for example to provide suggestions based on a partial
        value for a relational field. Sometimes be seen as the inverse
        function of :meth:`~.name_get`, but it is not guaranteed to be.

        :param str name: the name pattern to match
        :param list args: optional search domain (as for :meth:`~.search`)
        :param str operator: domain operator for matching ``name``, such as
                           ``'like'`` or ``'='``.
        :param int limit: optional max number of records to return
        :rtype: list
        :return: list of ``(id, text_repr)`` pairs containing the ids of the
                matching records and their textual representation.
        """
        if args is None:
            args = []

        # build search domain
        domain = args[:]
        if name:
            domain.append((self._rec_name or 'name', operator, name))

        # search for records
        records = self.search(domain, limit=limit)

        # return name_get result
        return records.name_get()

    @api.model
    def name_create(self, name) -> tuple:
        """ name_create(name) -> record

        Create a new record by calling :meth:`~.create` with only one value
        provided: the display name of the new record.

        The new record will be initialized with any default values
        applicable to this model, or provided through the context. The usual
        behavior is to use the default value for the field whose name is
        ``_rec_name``, in case the provided name is not a valid value for that field.

        :param name: display name of the record to create
        :rtype: tuple
        :return: the :meth:`~.name_get` pair value of the created record
        """
        if self._rec_name:
            record = self.create({self._rec_name: name})
            return record.name_get()[0]
        else:
            _logger.warning("Cannot create record for model %s, no _rec_name field defined", self._name)
            return False
