# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Access control mixin for Odoo models.

This module contains access control functionality extracted from the monolithic models.py file.
"""

import typing
from .. import api
from ..exceptions import AccessError

if typing.TYPE_CHECKING:
    from odoo.api import Self


class AccessMixin:
    """Mixin class containing access control methods for BaseModel."""

    def check_access(self, operation: str) -> None:
        """ Verify that the current user is allowed to perform ``operation`` on
        the records ``self``. If the operation is 'read', 'write' or 'create',
        ``operation`` is checked both against the model's access rights and
        record rules (if any). For other operations, only access rights are
        verified.

        :param operation: one of ``create``, ``read``, ``write``, ``unlink``
        :raise AccessError: if the operation is forbidden
        """
        # check access rights
        self.check_access_rights(operation, raise_exception=True)
        
        # check access rules for CRUD operations
        if operation in ('create', 'read', 'write', 'unlink'):
            self.check_access_rule(operation)

    def has_access(self, operation: str) -> bool:
        """ Return whether the current user is allowed to perform ``operation``
        on the records ``self``. If the operation is 'read', 'write' or 'create',
        ``operation`` is checked both against the model's access rights and
        record rules (if any). For other operations, only access rights are
        verified.

        :param operation: one of ``create``, ``read``, ``write``, ``unlink``
        :return: whether the operation is allowed
        """
        try:
            self.check_access(operation)
            return True
        except AccessError:
            return False

    def _filtered_access(self, operation: str):
        """ Return the subset of ``self`` for which the current user is allowed
        to perform ``operation``.

        :param operation: one of ``create``, ``read``, ``write``, ``unlink``
        :return: subset of accessible records
        """
        if self.env.su:
            return self

        # check access rights first
        if not self.check_access_rights(operation, raise_exception=False):
            return self.browse()

        # filter by access rules
        return self._filter_access_rules(operation)

    def _check_access(self, operation: str):
        """ Return ``None`` if the current user has permission to perform
        ``operation`` on ``self``, or a function that filters out forbidden
        records.

        :param operation: one of ``create``, ``read``, ``write``, ``unlink``
        :return: None or filtering function
        """
        if self.env.su:
            return None

        # check access rights
        if not self.check_access_rights(operation, raise_exception=False):
            def no_access(records):
                return records.browse()
            return no_access

        # check access rules
        def filter_access(records):
            return records._filter_access_rules(operation)

        return filter_access

    @api.model
    def check_access_rights(self, operation, raise_exception=True):
        """ Verify that the given operation is allowed for the current user according to ir.model.access.

        :param operation: one of ``create``, ``read``, ``write``, ``unlink``
        :param raise_exception: whether to raise an exception if access is denied
        :return: whether the operation is allowed
        :raise AccessError: if the operation is forbidden and ``raise_exception`` is True
        """
        if self.env.su:
            return True

        # get access rights from ir.model.access
        Access = self.env['ir.model.access']
        access_model = Access.check(self._name, operation, raise_exception=False)
        
        if not access_model and raise_exception:
            raise AccessError(_(
                'The requested operation cannot be completed due to security restrictions. '
                'Please contact your system administrator.\n\n'
                '(Document type: %(document_kind)s, Operation: %(operation)s)',
                document_kind=self._description or self._name,
                operation=operation
            ))

        return access_model

    def check_access_rule(self, operation):
        """ Verify that the given operation is allowed for the current user according to ir.rules.

        :param operation: one of ``create``, ``read``, ``write``, ``unlink``
        :raise AccessError: if the operation is forbidden
        """
        if self.env.su or not self:
            return

        # check if all records pass the access rules
        accessible_records = self._filter_access_rules(operation)
        if len(accessible_records) != len(self):
            forbidden_records = self - accessible_records
            raise AccessError(_(
                'The requested operation cannot be completed due to security restrictions. '
                'Please contact your system administrator.\n\n'
                '(Document type: %(document_kind)s, Operation: %(operation)s, Records: %(records)s)',
                document_kind=self._description or self._name,
                operation=operation,
                records=forbidden_records.ids
            ))

    def _filter_access_rules(self, operation):
        """ Return the subset of ``self`` for which ``operation`` is allowed. """
        if self.env.su or not self:
            return self

        # get domain from ir.rules
        Rule = self.env['ir.rule']
        domain = Rule._compute_domain(self._name, operation)
        
        if not domain:
            return self

        # filter records by domain
        return self.filtered_domain(domain)

    def _filter_access_rules_python(self, operation):
        """ Deprecated method - use _filter_access_rules instead. """
        import warnings
        warnings.warn(
            "_filter_access_rules_python is deprecated, use _filter_access_rules",
            DeprecationWarning, stacklevel=2
        )
        return self._filter_access_rules(operation)

    def _check_company_domain(self, companies):
        """Domain to be used for company consistency between records regarding this model.
        
        :param companies: recordset of res.company
        :return: domain to be used to filter records
        """
        if not companies:
            return []
        
        # default implementation - can be overridden by models
        if 'company_id' in self._fields:
            return [('company_id', 'in', companies.ids)]
        elif 'company_ids' in self._fields:
            return [('company_ids', 'in', companies.ids)]
        else:
            return []

    def _check_company(self, fnames=None):
        """ Check the companies of the values of the given field names.

        :param fnames: names of the fields to check (default: all relational fields)
        """
        if not self or self.env.su:
            return

        if fnames is None:
            fnames = [name for name, field in self._fields.items() 
                     if field.type in ('many2one', 'many2many') and field.check_company]

        current_company = self.env.company
        if not current_company:
            return

        for fname in fnames:
            field = self._fields.get(fname)
            if not field or not field.check_company:
                continue

            for record in self:
                value = record[fname]
                if not value:
                    continue

                if field.type == 'many2one':
                    related_companies = value.company_id if 'company_id' in value._fields else value.company_ids if 'company_ids' in value._fields else self.env['res.company']
                elif field.type == 'many2many':
                    related_companies = self.env['res.company']
                    for v in value:
                        if 'company_id' in v._fields:
                            related_companies |= v.company_id
                        elif 'company_ids' in v._fields:
                            related_companies |= v.company_ids

                if related_companies and current_company not in related_companies:
                    raise ValidationError(_(
                        'The record %(record)s is not in the same company as the current user. '
                        'Please check the company settings.',
                        record=record.display_name
                    ))

    def user_has_groups(self, groups):
        """ Check if the current user belongs to at least one of the given groups.

        :param groups: comma-separated list of group external IDs
        :return: True if user belongs to at least one group, False otherwise
        """
        if self.env.su:
            return True

        if not groups:
            return True

        # parse groups string
        group_list = [g.strip() for g in groups.split(',')]
        
        # check if user has any of the groups
        user = self.env.user
        for group_ext_id in group_list:
            if group_ext_id == '.':
                # special case: no group required
                return False
            try:
                group = self.env.ref(group_ext_id)
                if group in user.groups_id:
                    return True
            except ValueError:
                # group doesn't exist
                continue

        return False

    def _get_redirect_suggested_company(self):
        """Return the suggested company to be set on the context
        in case of access error on the record.
        """
        if 'company_id' in self._fields:
            companies = self.mapped('company_id')
        elif 'company_ids' in self._fields:
            companies = self.mapped('company_ids')
        else:
            companies = self.env['res.company']

        if len(companies) == 1:
            return companies
        else:
            return self.env['res.company']
