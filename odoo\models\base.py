# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Core BaseModel class for Odoo models.

This module contains the refactored BaseModel class that combines all the mixins
to provide the same functionality as the original monolithic models.py file.
"""

from __future__ import annotations

import typing
from collections import defaultdict
from collections.abc import Reversible

from .. import api
from ..tools import frozendict, OrderedSet

# Import all the mixins
from .cache import CacheMixin
from .fields_mixin import FieldsMixin
from .crud_mixin import CRUDMixin
from .search_mixin import SearchMixin
from .access_mixin import AccessMixin
from .recordset_mixin import RecordsetMixin
from .setup_mixin import SetupMixin

if typing.TYPE_CHECKING:
    from ..modules.registry import Registry
    from odoo.api import Self, ValuesType, IdType
    from ..fields import Field


class MetaModel(type):
    """Metaclass for Odoo models."""
    
    def __new__(meta, name, bases, attrs):
        # this prevents assignment of non-fields on recordsets
        if attrs.get('_register', True):
            for key, val in attrs.items():
                if not key.startswith('_') and not callable(val):
                    # check if it's a field
                    if not hasattr(val, '_setup_attrs'):
                        attrs[key] = None  # prevent assignment
        
        return super().__new__(meta, name, bases, attrs)

    def __init__(self, name, bases, attrs):
        super().__init__(name, bases, attrs)

        # initialize _fields if not present
        if not hasattr(self, '_fields'):
            self._fields = {}

        # add magic fields for model definitions
        if not self._abstract and hasattr(self, '_name'):
            # this class defines a model: add magic fields
            def add(name, field):
                setattr(self, name, field)
                self._fields[name] = field

            def add_default(name, field):
                if name not in attrs:
                    add(name, field)

            # add default fields
            if self._auto:
                from ..fields import Id, Datetime, Many2one
                add_default('id', Id())
                if self._log_access:
                    add_default('create_date', Datetime(string='Created on', readonly=True))
                    add_default('create_uid', Many2one('res.users', string='Created by', readonly=True))
                    add_default('write_date', Datetime(string='Last Updated on', readonly=True))
                    add_default('write_uid', Many2one('res.users', string='Last Updated by', readonly=True))


class BaseModel(
    CacheMixin,
    FieldsMixin,
    CRUDMixin,
    SearchMixin,
    AccessMixin,
    RecordsetMixin,
    SetupMixin,
    metaclass=MetaModel
):
    """Base class for Odoo models.

    Odoo models are created by inheriting one of the following:

    *   :class:`Model` for regular database-persisted models

    *   :class:`TransientModel` for temporary data, stored in the database but
        automatically vacuumed every so often

    *   :class:`AbstractModel` for abstract super classes meant to be shared by
        multiple inheriting models

    The system automatically instantiates every model once per database. Those
    instances represent the available models on each database, and depend on
    which modules are installed on that database. The actual class of each
    instance is built from the Python classes that create and inherit from the
    corresponding model.

    Every model instance is a "recordset", i.e., an ordered collection of
    records of the model. Recordsets are returned by methods like
    :meth:`~.browse`, :meth:`~.search`, or field accesses. Records have no
    explicit representation: a record is represented as a recordset of one
    record.

    To create a class that should not be instantiated,
    the :attr:`~odoo.models.BaseModel._register` attribute may be set to False.
    """
    __slots__ = ['env', '_ids', '_prefetch_ids']

    env: api.Environment
    id: IdType | typing.Literal[False]
    display_name: str | typing.Literal[False]
    pool: Registry

    _fields: dict[str, 'Field']
    _auto = False
    """Whether a database table should be created.
    If set to ``False``, override :meth:`~odoo.models.BaseModel.init`
    to create the database table.

    Automatically defaults to `True` for :class:`Model` and
    :class:`TransientModel`, `False` for :class:`AbstractModel`.

    .. tip:: To create a model without any table, inherit
            from :class:`~odoo.models.AbstractModel`.
    """
    _register = False           #: registry visibility
    _abstract = True
    """ Whether the model is *abstract*.

    .. seealso:: :class:`AbstractModel`
    """
    _transient = False
    """ Whether the model is *transient*.

    .. seealso:: :class:`TransientModel`
    """

    _name: str | None = None            #: the model name (in dot-notation, module namespace)
    _description: str | None = None     #: the model's informal name
    _module = None                      #: the model's module (in the Odoo sense)
    _custom = False                     #: should be True for custom models only

    _inherit: str | list[str] | tuple[str, ...] = ()
    """Python-inherited models:

    :type: str or list(str) or tuple(str)

    .. note::

        * If :attr:`._name` is set, name(s) of parent models to inherit from
        * If :attr:`._name` is unset, name of a single model to extend in-place
    """
    _inherits = frozendict()
    """dictionary {'parent_model': 'm2o_field'} mapping the _name of the parent business
    objects to the names of the corresponding foreign key fields to use::

      _inherits = {
          'a.model': 'a_field_id',
          'b.model': 'b_field_id'
      }

    implements composition-based inheritance: the new model exposes all
    the fields of the inherited models but stores none of them:
    the values themselves remain stored on the linked record.

    .. warning::

        if multiple fields with the same name are defined in the
        :attr:`~odoo.models.BaseModel._inherits`-ed models, the inherited
        field will correspond to the last one (in the inherits list order).
    """

    _table: str | None = None           #: SQL table name used by model if :attr:`_auto`
    _table_query: str | None = None     #: SQL expression of the table's content (optional)

    _sequence: str | None = None        #: SQL sequence to use for ID field
    _sql_constraints: list = []         #: SQL constraints [(name, sql_def, message)]

    _check_company_auto = False
    """Whether to automatically check company consistency for the model."""

    _parent_name = 'parent_id'          #: the many2one field used as parent field
    _parent_store = False               #: set to True to compute parent_path field
    _date_name = 'date'                 #: field to use for default calendar view
    _fold_name = 'fold'                 #: field to determine folded groups in kanban views

    _needaction = False                 #: whether the model supports "need actions" (deprecated)

    _order = 'id'                       #: default order field for searching results
    _rec_name = None                    #: field to use for the textual representation of records
    _rec_names_search = None            #: fields to search for in name_search

    _active_name = None                 #: name of the field used to filter active records

    _log_access = True
    """Whether to automatically log access to records of this model.
    If enabled, four fields will be created in the database table:
    ``create_date``, ``create_uid``, ``write_date``, ``write_uid``.
    """

    def __init__(self, env: api.Environment, ids: tuple[IdType, ...], prefetch_ids: Reversible[IdType]):
        """ Create a recordset instance.

        :param env: an environment
        :param ids: a tuple of record ids
        :param prefetch_ids: a reversible collection of record ids (for prefetching)
        """
        object.__setattr__(self, 'env', env)
        object.__setattr__(self, '_ids', ids)
        object.__setattr__(self, '_prefetch_ids', prefetch_ids)

    @property
    def ids(self) -> list[int]:
        """ Return the list of actual record ids corresponding to ``self``. """
        return [id for id in self._ids if isinstance(id, int)]

    @property
    def _origin(self) -> Self:
        """ Return the actual records corresponding to ``self``. """
        # return records with actual ids only
        actual_ids = tuple(id for id in self._ids if isinstance(id, int))
        return self.__class__(self.env, actual_ids, self._prefetch_ids)

    def _as_query(self, ordered=True):
        """ Return a :class:`Query` that corresponds to the recordset ``self``.
        """
        from ..tools import Query
        query = Query(self.env.cr, self._table, self._table_sql)
        if self._ids:
            query = query.where(f"id IN {tuple(self.ids)}")
        return query

    @api.depends(lambda self: (self._rec_name,) if self._rec_name else ())
    def _compute_display_name(self):
        """Compute the value of the `display_name` field.

        In general `display_name` is equal to calling `name_get()[0][1]`.
        """
        names = dict(self.name_get())
        for record in self:
            record.display_name = names.get(record.id, False)

    def name_get(self) -> list[tuple[int, str]]:
        """ Return a textual representation for the records in ``self``.
        By default this is the value of the ``display_name`` field.

        :return: list of ``(id, text_repr)`` for each record
        """
        result = []
        name = self._rec_name
        if name in self._fields:
            convert = self._fields[name].convert_to_display_name
            for record in self:
                result.append((record.id, convert(record[name], record)))
        else:
            for record in self:
                result.append((record.id, f"{self._name},{record.id}"))

        return result

    @api.model
    def default_get(self, fields_list):
        """ default_get(fields_list) -> default_values

        Return default values for the fields in ``fields_list``. Default
        values are determined by the context, user defaults, and the model
        itself.

        :param fields_list: list of field names
        :return: dictionary mapping field names to their default values
        """
        defaults = {}
        parent_fields = defaultdict(list)
        ir_defaults = self.env['ir.default'].get_model_defaults(self._name)

        for name in fields_list:
            # 1. look up context
            key = 'default_' + name
            if key in self._context:
                defaults[name] = self._context[key]
                continue

            # 2. look up ir_default
            if name in ir_defaults:
                defaults[name] = ir_defaults[name]
                continue

            # 3. look up field.default
            field = self._fields.get(name)
            if field:
                if field.default:
                    defaults[name] = field.default(self)
                    continue

                # 4. delegate to parent model
                if self._inherits:
                    for parent_model, parent_field in self._inherits.items():
                        if name in self.env[parent_model]._fields:
                            parent_fields[parent_model].append(name)

        # get defaults from parent models
        for parent_model, field_names in parent_fields.items():
            parent_defaults = self.env[parent_model].default_get(field_names)
            defaults.update(parent_defaults)

        return defaults

    def _get_base_lang(self):
        """ Returns the base language of the record. """
        return self.env.lang or 'en_US'

    def get_metadata(self):
        """Return some metadata about the given records.

        :return: list of ownership dictionaries for each requested record
        """
        IrAttachment = self.env['ir.attachment'].sudo()
        if self.env.user._has_group('base.group_no_one'):
            xml_ids = dict((res_id, xmlid) for xmlid, res_id in self._get_external_ids().items())
        else:
            xml_ids = {}

        result = []
        for record in self:
            result.append({
                'id': record.id,
                'create_date': record.create_date if 'create_date' in record._fields else False,
                'create_uid': (record.create_uid.id, record.create_uid.name) if 'create_uid' in record._fields and record.create_uid else False,
                'write_date': record.write_date if 'write_date' in record._fields else False,
                'write_uid': (record.write_uid.id, record.write_uid.name) if 'write_uid' in record._fields and record.write_uid else False,
                'xmlid': xml_ids.get(record.id, ''),
                'noupdate': False,  # TODO: implement this
            })

        return result

    def get_base_url(self):
        """ Return rooturl for a specific record.

        By default, it returns the ir.config_parameter of base_url
        but it can be overridden by model.
        """
        return self.env['ir.config_parameter'].sudo().get_param('web.base.url')

    @classmethod
    def is_transient(cls):
        """ Return whether the model is transient.

        See :class:`TransientModel`.
        """
        return cls._transient

    def toggle_active(self):
        "Inverses the value of :attr:`active` on the records in ``self``."
        active_name = self._active_name
        if not active_name:
            return
        for record in self:
            record[active_name] = not record[active_name]

    def action_archive(self):
        """Sets :attr:`active` to ``False`` on a recordset, by calling
        :meth:`~BaseModel.toggle_active` only on the records that are currently
        active.
        """
        active_name = self._active_name
        if active_name:
            self.filtered(active_name).toggle_active()

    def action_unarchive(self):
        """Sets :attr:`active` to ``True`` on a recordset, by calling
        :meth:`~BaseModel.toggle_active` only on the records that are currently
        inactive.
        """
        active_name = self._active_name
        if active_name:
            self.filtered(lambda r: not r[active_name]).toggle_active()


# Aliases for backward compatibility
AbstractModel = BaseModel
