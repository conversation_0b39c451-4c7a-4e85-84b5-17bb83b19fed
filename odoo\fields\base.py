# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

""" Base field classes and metaclass. """
from __future__ import annotations

import itertools
import typing

from ..tools.misc import Sentinel, SENTINEL
from ..tools import lazy_property

from odoo.api import ContextType, DomainType, IdType, NewId, M, T
from odoo.exceptions import AccessError


class MetaField(type):
    """ Metaclass for field classes. """
    by_type = {}

    def __init__(cls, name, bases, attrs):
        super(MetaField, cls).__init__(name, bases, attrs)
        if not hasattr(cls, 'type'):
            return

        if cls.type and cls.type not in MetaField.by_type:
            MetaField.by_type[cls.type] = cls

        # compute class attributes to avoid calling dir() on fields
        cls.related_attrs = []
        cls.description_attrs = []
        for attr in dir(cls):
            if attr.startswith('_related_'):
                cls.related_attrs.append((attr[9:], attr))
            elif attr.startswith('_description_'):
                cls.description_attrs.append((attr[13:], attr))


_global_seq = iter(itertools.count())


class Field(MetaField('DummyField', (object,), {}), typing.Generic[T]):
    """The field descriptor contains the field definition, and manages accesses
    and assignments of the corresponding field on records. The following
    attributes may be provided when instantiating a field:

    :param str string: the label of the field seen by users; if not
        set, the ORM takes the field name in the class (capitalized).

    :param str help: the tooltip of the field seen by users

    :param bool readonly: whether the field is readonly (default: ``False``)

        This only has an impact on the UI. Any field assignation in code will work
        (if the field is a stored field or an inversable one).

    :param bool required: whether the value of the field is required (default: ``False``)

    :param str index: whether the field is indexed in database, and the kind of index.
        Note: this has no effect on non-stored and virtual fields.
        The possible values are:

        * ``"btree"`` or ``True``: standard index, good for many2one
        * ``"btree_not_null"``: BTREE index without NULL values (useful when most
                                values are NULL, or when NULL is never searched for)
        * ``"trigram"``: Generalized Inverted Index (GIN) with trigrams (good for full-text search)
        * ``None`` or ``False``: no index (default)

    :param default: the default value for the field; this is either a static
        value, or a function taking a recordset and returning a value; use
        ``default=None`` to discard default values for the field
    :type default: value or callable

    :param str groups: comma-separated list of group xml ids (string); this
        restricts the field access to the users of the given groups only

    :param bool company_dependent: whether the field value is dependent of the current company;

        The value is stored on the model table as jsonb dict with the company id as the key.

        The field's default values stored in model ir.default are used as fallbacks for
        unspecified values in the jsonb dict.

    :param bool copy: whether the field value should be copied when the record
        is duplicated (default: ``True`` for normal fields, ``False`` for
        ``one2many`` and computed fields, including property fields and
        related fields)

    :param bool store: whether the field is stored in database
        (default:``True``, ``False`` for computed fields)

    :param str aggregator: aggregate function used by :meth:`~odoo.models.Model.read_group`
        when grouping on this field.

        Supported aggregate functions are:

        * ``array_agg`` : values, including nulls, concatenated into an array
        * ``count`` : number of rows
        * ``count_distinct`` : number of distinct rows
        * ``bool_and`` : true if all values are true, otherwise false
        * ``bool_or`` : true if at least one value is true, otherwise false
        * ``max`` : maximum value of all values
        * ``min`` : minimum value of all values
        * ``avg`` : the average (arithmetic mean) of all values
        * ``sum`` : sum of all values

    :param str group_expand: function used to expand read_group results when grouping on
        the current field. For selection fields, ``group_expand=True`` automatically
        expands groups for all selection keys.

        .. code-block:: python

            @api.model
            def _read_group_selection_field(self, values, domain):
                return ['choice1', 'choice2', ...] # available selection choices.

            @api.model
            def _read_group_many2one_field(self, records, domain):
                return records + self.search([custom_domain])

    .. rubric:: Computed Fields

    :param str compute: name of a method that computes the field

        .. seealso:: :ref:`Advanced Fields/Compute fields <reference/fields/compute>`

    :param bool precompute: whether the field should be computed before record insertion
        in database.  Should be used to specify manually some fields as precompute=True
        when the field can be computed before record insertion.
        (e.g. avoid statistics fields based on search/read_group), many2one
        linking to the previous record, ... (default: `False`)

        .. warning::

            Precomputation only happens when no explicit value and no default
            value is provided to create().  This means that a default value
            disables the precomputation, even if the field is specified as
            precompute=True.

            Precomputing a field can be counterproductive if the records of the
            given model are not created in batch.  Consider the situation were
            many records are created one by one.  If the field is not
            precomputed, it will normally be computed in batch at the flush(),
            and the prefetching mechanism will help making the computation
            efficient.  On the other hand, if the field is precomputed, the
            computation will be made one by one, and will therefore not be able
            to take advantage of the prefetching mechanism.

            Following the remark above, precomputed fields can be interesting on
            the lines of a one2many, which are usually created in batch by the
            ORM itself, provided that they are created by writing on the record
            that contains them.

    :param bool compute_sudo: whether the field should be recomputed as superuser
        to bypass access rights (by default ``True`` for stored fields, ``False``
        for non stored fields)

    :param bool recursive: whether the field has recursive dependencies (the field
        ``X`` has a dependency like ``parent_id.X``); declaring a field recursive
        must be explicit to guarantee that recomputation is correct

    :param str inverse: name of a method that inverses the field (optional)

    :param str search: name of a method that implement search on the field (optional)

    :param str related: sequence of field names

    :param bool default_export_compatible: whether the field must be exported by default in an import-compatible export

        .. seealso:: :ref:`Advanced fields/Related fields <reference/fields/related>`
    """

    type: str                           # type of the field (string)
    relational = False                  # whether the field is a relational one
    translate = False                   # whether the field is translated

    write_sequence = 0  # field ordering for write()
    # Database column type (ident, spec) for non-company-dependent fields.
    # Company-dependent fields are stored as jsonb (see column_type).
    _column_type: typing.Tuple[str, str] | None = None

    _args__ = None                      # the parameters given to __init__()
    _module = None                      # the field's module name
    _modules = None                     # modules that define this field
    _setup_done = True                  # whether the field is completely set up
    _sequence = None                    # absolute ordering of the field
    _base_fields = ()                   # the fields defining self, in override order
    _extra_keys = ()                    # unknown attributes set on the field
    _direct = False                     # whether self may be used directly (shared)
    _toplevel = False                   # whether self is on the model's registry class

    automatic = False                   # whether the field is automatically created ("magic" field)
    inherited = False                   # whether the field is inherited (_inherits)
    inherited_field = None              # the corresponding inherited field

    name: str                           # name of the field
    model_name: str | None = None       # name of the model of this field
    comodel_name: str | None = None     # name of the model of values (if relational)

    string: str | None = None           # field label
    help: str | None = None             # field tooltip
    readonly = False                    # whether the field is readonly
    required = False                    # whether the field is required
    index = None                        # whether the field is indexed, and how
    default = None                      # default value
    groups = None                       # csv list of group xml ids
    company_dependent = False           # whether the field is company-dependent
    copy = True                         # whether the field is copied over by BaseModel.copy()
    _depends = None                     # collection of field dependencies
    _depends_context = None             # collection of context key dependencies
    recursive = False                   # whether self depends on itself

    store = True                        # whether the field is stored in database
    index = None                        # how the field is indexed in database
    manual = False                      # whether the field is a custom field
    copy = True                         # whether the field is copied over by BaseModel.copy()
    _depends = None                     # collection of field dependencies
    _depends_context = None             # collection of context key dependencies
    recursive = False                   # whether self depends on itself

    compute = None                      # compute method (name or callable)
    compute_sudo = None                 # whether to compute the field as admin
    inverse = None                      # inverse method (name or callable)
    search = None                       # search method (name or callable)
    related = None                      # sequence of field names
    default_export_compatible = True    # whether the field is exported by default

    aggregator = None                   # aggregate function for read_group
    group_expand = None                 # group expand function for read_group
    prefetch = True                     # whether the field is prefetched
    precompute = False                  # whether the field is precomputed
    context_dependent = False           # whether the field depends on context

    # properties for company-dependent fields
    _related_company_dependent = None
    _description_company_dependent = None

    def __init__(self, string: str | Sentinel = SENTINEL, **kwargs):
        kwargs['string'] = string
        args = {key: val for key, val in kwargs.items() if val is not SENTINEL}
        self._sequence = next(_global_seq)
        self._args__ = args
        self._setup_done = False

    def __str__(self):
        return f"{self.model_name}.{self.name}"

    def __repr__(self):
        return f"<field '{self.model_name}.{self.name}'>"

    #
    # Base field setup: things that do not depend on other models/fields
    #
    # The base field setup is done by field.__set_name__(), which determines the
    # field's name, model name, module and its parameters.
    #
    # The dictionary field._args__ gives the parameters passed to the field's
    # constructor.  Most parameters have an attribute of the same name on the
    # field.  The parameters as attributes are assigned by the field setup.
    #
    # When several definition classes of the same model redefine a given field,
    # the field occurrences are "merged" into one new field instantiated at
    # runtime on the registry class of the model.  The occurrences of the field
    # are given to the new field as the parameter '_base_fields'; it is a list
    # of fields in override order (or reverse MRO).
    #
    # In order to save memory, a field should avoid having field._args__ and/or
    # many attributes when possible.  We call "direct" a field that can be set
    # up directly from its definition class.  Direct fields are non-related
    # fields defined on models, and can be shared across registries.  We call
    # "toplevel" a field that is put on the model's registry class, and is
    # therefore specific to the registry.
    #
    # Toplevel field are set up once, and are no longer set up from scratch
    # after that.  Those fields can save memory by discarding field._args__ and
    # field._base_fields once set up, because those are no longer necessary.
    #
    # Non-toplevel non-direct fields are the fields on definition classes that
    # may not be shared.  In other words, those fields are never used directly,
    # and are always recreated as toplevel fields.  On those fields, the base
    # setup is useless, because only field._args__ is used for setting up other
    # fields.  We therefore skip the base setup for those fields.  The only
    # attributes of those fields are: '_sequence', '_args__', 'model_name', 'name'
    # and '_module', which makes their __dict__'s size minimal.

    def __set_name__(self, owner, name):
        """ Perform the base setup of a field.

        :param owner: the owner class of the field (the model's definition or registry class)
        :param name: the name of the field
        """
        # Import here to avoid circular imports
        from ..models import BaseModel
        from ..models.utils import is_definition_class

        assert issubclass(owner, BaseModel)
        self.model_name = owner._name
        self.name = name
        if is_definition_class(owner):
            # only for fields on definition classes, not registry classes
            self._module = owner._module
            owner._field_definitions.append(self)

        if not self._args__.get('related'):
            self._direct = True
        if self._direct or self._toplevel:
            self._setup_attrs(owner, name)
            if self._toplevel:
                # free memory, self._args__ and self._base_fields are no longer useful
                self.__dict__.pop('_args__', None)
                self.__dict__.pop('_base_fields', None)

    #
    # Setup field parameter attributes
    #

    def _get_attrs(self, model_class, name):
        """ Return the field parameter attributes as a dictionary. """
        # determine all inherited field attributes
        attrs = {}
        modules = []
        for field in self._args__.get('_base_fields', ()):
            if not isinstance(self, type(field)):
                # 'self' overrides 'field' and their types are not compatible;
                # so we ignore all the parameters collected so far
                attrs.clear()
                modules.clear()
                continue
            attrs.update(field._args__)
            if field._module:
                modules.append(field._module)
        attrs.update(self._args__)
        if self._module:
            modules.append(self._module)

        attrs['_args__'] = dict(self._args__)
        attrs['model_name'] = model_class._name
        attrs['name'] = name
        attrs['_module'] = modules[-1] if modules else None
        attrs['_modules'] = tuple(set(modules))

        # initialize ``self`` with ``attrs``
        if name == 'state':
            # by default, `state` fields should be reset on copy
            attrs['copy'] = attrs.get('copy', False)
        if attrs.get('compute'):
            # by default, computed fields are not stored, computed in superuser
            # mode if stored, not copied (unless stored and explicitly not
            # readonly), and readonly (unless inversible)
            attrs['store'] = store = attrs.get('store', False)
            attrs['compute_sudo'] = attrs.get('compute_sudo', store)
            if not (attrs['store'] and not attrs.get('readonly', True)):
                attrs['copy'] = attrs.get('copy', False)
            attrs['readonly'] = attrs.get('readonly', not attrs.get('inverse'))
        if attrs.get('related'):
            # by default, related fields are not stored, computed in superuser
            # mode, not copied and readonly
            attrs['store'] = attrs.get('store', False)
            attrs['compute_sudo'] = attrs.get('compute_sudo', True)
            attrs['copy'] = attrs.get('copy', False)
            attrs['readonly'] = attrs.get('readonly', True)
        if attrs.get('company_dependent'):
            # by default, company-dependent fields are not copied
            attrs['copy'] = attrs.get('copy', False)

        return attrs

    def _setup_attrs(self, model_class, name):
        """ Setup the field parameter attributes. """
        attrs = self._get_attrs(model_class, name)
        self._extra_keys = tuple(key for key in attrs if not hasattr(Field, key))
        for key, val in attrs.items():
            setattr(self, key, val)

    @lazy_property
    def column_type(self):
        """ Return the column type and format for this field. """
        if self.company_dependent:
            return ('jsonb', 'jsonb')
        return self._column_type

    @lazy_property
    def column_format(self):
        """ Return the column format for this field. """
        return self.column_type[1] if self.column_type else None

    @lazy_property
    def column_cast_from(self):
        """ Return the column types from which this field can be cast. """
        return ()

    def new(self, **kwargs):
        """ Return a field like ``self`` with the given parameters ``kwargs``. """
        # determine the class to use for the new field
        if 'type' in kwargs:
            if kwargs['type'] != self.type:
                cls = MetaField.by_type.get(kwargs['type'], Field)
            else:
                cls = type(self)
        else:
            cls = type(self)

        # create the new field with a copy of self's parameters
        kwargs = dict(self._args__, **kwargs)
        kwargs['_base_fields'] = self._args__.get('_base_fields', ()) + (self,)
        field = cls(**kwargs)
        field._direct = False
        return field

    def setup(self, model):
        """ Perform the complete setup of a field. """
        if not self._setup_done:
            # validate field params
            for key in self._extra_keys:
                if not model._valid_field_parameter(self, key):
                    import logging
                    _logger = logging.getLogger(__name__)
                    _logger.warning(
                        "Field %s: unknown parameter %r, if this is an actual"
                        " parameter you may want to override the method"
                        " _valid_field_parameter on the relevant model in order to"
                        " allow it",
                        self, key
                    )
            if self.related:
                self.setup_related(model)
            else:
                self.setup_nonrelated(model)

            if not isinstance(self.required, bool):
                import warnings
                warnings.warn(f'Property {self}.required should be a boolean ({self.required}).')

            if not isinstance(self.readonly, bool):
                import warnings
                warnings.warn(f'Property {self}.readonly should be a boolean ({self.readonly}).')

            self._setup_done = True

    def setup_nonrelated(self, model):
        """ Setup a non-related field on the given model. """
        pass

    def setup_related(self, model):
        """ Setup a related field on the given model. """
        pass

    def __get__(self, record, owner=None) -> T:
        """ return the value of field ``self`` on ``record`` """
        if record is None:
            return self         # the field is accessed through the owner class

        # only a single record may be accessed
        record.ensure_one()
        env = record.env

        # determine the value of self for record
        if self.compute and not (self.store and record.id):
            # non-stored computed field or new record: compute the value
            try:
                record._prefetch_field(self)
            except AccessError:
                # lack of access rights: retrieve the value in the cache only
                pass

        try:
            return record._cache[self]
        except KeyError:
            pass

        # cache miss, determine the value
        if self.store and record.id:
            # stored field: fetch from database
            record._fetch_field(self)
        elif self.compute:
            # computed field: compute the value
            record._prefetch_field(self)
        else:
            # non-stored field without value: use default value
            value = self.default(record) if callable(self.default) else self.default
            record._cache[self] = self.convert_to_cache(value, record)

        return record._cache[self]

    def __set__(self, record, value):
        """ set the value of field ``self`` on ``record`` """
        # only a single record may be assigned
        record.ensure_one()
        env = record.env

        if self.readonly and self.store and record.id:
            # readonly stored field: check access rights
            if not env.su and not record._is_readonly_field_writable(self):
                raise AccessError(
                    f"Field '{self}' is readonly and cannot be modified."
                )

        # convert and validate the value
        value = self.convert_to_cache(value, record)

        # update the cache and inverse fields
        record._cache[self] = value
        record._inverse_field(self)

    def convert_to_cache(self, value, record, validate=True):
        """ Convert ``value`` to the cache format. """
        return value

    def convert_to_record(self, value, record):
        """ Convert ``value`` from the cache format to the record format. """
        return value

    def convert_to_read(self, value, record, use_display_name=True):
        """ Convert ``value`` from the record format to the format returned by read(). """
        return self.convert_to_record(value, record)

    def convert_to_write(self, value, record):
        """ Convert ``value`` from any format to the format of write(). """
        cache_value = self.convert_to_cache(value, record, validate=False)
        record_value = self.convert_to_record(cache_value, record)
        return record_value

    def convert_to_column(self, value, record, values=None, validate=True):
        """ Convert ``value`` to the database column format. """
        return self.convert_to_cache(value, record, validate)

    def convert_to_export(self, value, record):
        """ Convert ``value`` to the export format. """
        if not value:
            return ''
        return self.convert_to_record(value, record)

    def determine_inverse(self, records):
        """ Given the value of ``self`` on ``records``, inverse the computation. """
        from .utils import determine
        determine(self.inverse, records)

    def determine_domain(self, records, operator, value):
        """ Return a domain representing a condition on ``self``. """
        from .utils import determine
        return determine(self.search, records, operator, value)
