# Part of Odoo. See LICENSE file for full copyright and licensing details.

""" Session management for HTTP requests. """

import collections.abc
import contextlib
import json
import logging
import os
import time

from ..service import security
from ..tools._vendor import sessions

# Session lifetime constant
SESSION_LIFETIME = 60 * 60 * 24 * 7  # 7 days

_logger = logging.getLogger(__name__)


class SessionExpiredException(Exception):
    pass


def get_session_max_inactivity(env):
    if not env or env.cr._closed:
        return SESSION_LIFETIME

    ICP = env['ir.config_parameter'].sudo()

    try:
        return int(ICP.get_param('sessions.max_inactivity_seconds', SESSION_LIFETIME))
    except ValueError:
        _logger.warning("Invalid value for 'sessions.max_inactivity_seconds', using default value.")
        return SESSION_LIFETIME


class FilesystemSessionStore(sessions.FilesystemSessionStore):
    """ Place where to load and save session objects. """
    def get_session_filename(self, sid):
        # scatter sessions across 4096 (64^2) directories
        if not self.is_valid_key(sid):
            raise ValueError(f'Invalid session id {sid!r}')
        sha_dir = sid[:2]
        dirname = os.path.join(self.path, sha_dir)
        session_path = os.path.join(dirname, sid)
        return session_path

    def save(self, session):
        session_path = self.get_session_filename(session.sid)
        dirname = os.path.dirname(session_path)
        if not os.path.isdir(dirname):
            with contextlib.suppress(OSError):
                os.mkdir(dirname, 0o0755)
        super().save(session)

    def get(self, sid):
        # retro compatibility
        old_path = super().get_session_filename(sid)
        session_path = self.get_session_filename(sid)
        if os.path.isfile(old_path) and not os.path.isfile(session_path):
            dirname = os.path.dirname(session_path)
            if not os.path.isdir(dirname):
                with contextlib.suppress(OSError):
                    os.mkdir(dirname, 0o0755)
            with contextlib.suppress(OSError):
                os.rename(old_path, session_path)
        return super().get(sid)

    def rotate(self, session, env):
        self.delete(session)
        session.sid = self.generate_key()
        if session.uid and env:
            session.session_token = security.compute_session_token(session, env)
        session.should_rotate = False
        self.save(session)

    def vacuum(self):
        threshold = time.time() - SESSION_LIFETIME
        for fname in os.listdir(self.path):
            path = os.path.join(self.path, fname)
            try:
                if os.path.getmtime(path) < threshold:
                    os.unlink(path)
            except OSError:
                pass


class Session(collections.abc.MutableMapping):
    """ Structure containing data persisted across requests. """
    __slots__ = ('can_save', '_Session__data', 'is_dirty', 'is_new',
                 'should_rotate', 'sid')

    def __init__(self, data, sid, new=False):
        self.can_save = True
        self.__data = {}
        self.update(data)
        self.is_dirty = False
        self.is_new = new
        self.should_rotate = False
        self.sid = sid

    #
    # MutableMapping implementation with DocDict-like extension
    #
    def __getitem__(self, item):
        return self.__data[item]

    def __setitem__(self, item, value):
        value = json.loads(json.dumps(value))
        if item not in self.__data or self.__data[item] != value:
            self.is_dirty = True
        self.__data[item] = value

    def __delitem__(self, item):
        del self.__data[item]
        self.is_dirty = True

    def __iter__(self):
        return iter(self.__data)

    def __len__(self):
        return len(self.__data)

    def __getattr__(self, attr):
        return self[attr]

    def __setattr__(self, k, v):
        if k in Session.__slots__:
            object.__setattr__(self, k, v)
        else:
            self[k] = v

    def __delattr__(self, k):
        try:
            del self[k]
        except KeyError:
            raise AttributeError(k)

    def __repr__(self):
        return f"<{self.__class__.__name__} {dict(self)!r}>"

    def touch(self):
        self.is_dirty = True

    def authenticate(self, uid, password_crypt):
        """
        Authenticate the current session, setting the user's ID in the session.
        
        :param uid: user id
        :param password_crypt: encrypted password to store in session
        """
        self.uid = uid
        self.should_rotate = True

    def logout(self, keep_db=True):
        for k in list(self):
            if k != 'db' or not keep_db:
                del self[k]
        self.uid = None
        self.should_rotate = True

    def get_context(self):
        """
        Re-initializes the current session, and returns the session's context.
        """
        return self.context

    def save(self, store):
        if self.can_save:
            store.save_if_modified(self)

    def rotate(self, store, env):
        if self.should_rotate:
            store.rotate(self, env)

    @property
    def should_save(self):
        return self.is_dirty and self.can_save
