# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

""" Basic field types: Boolean, Integer, Float, Monetary, String-based fields. """
from __future__ import annotations

import typing
from xmlrpc.client import MAXINT

from .base import Field
from ..tools import float_repr, float_round, float_compare, float_is_zero, sql
from ..tools.sql import pg_varchar
from ..tools.misc import Sentinel, SENTINEL
from ..tools.translate import html_translate
from operator import attrgetter


class Boolean(Field[bool]):
    """ Encapsulates a :class:`bool`. """
    type = 'boolean'
    _column_type = ('bool', 'bool')

    def convert_to_column(self, value, record, values=None, validate=True):
        return bool(value)

    def convert_to_column_update(self, value, record):
        if self.company_dependent:
            value = {k: bool(v) for k, v in value.items()}
        return super().convert_to_column_update(value, record)

    def convert_to_cache(self, value, record, validate=True):
        return bool(value)

    def convert_to_export(self, value, record):
        return bool(value)


class Integer(Field[int]):
    """ Encapsulates an :class:`int`. """
    type = 'integer'
    _column_type = ('int4', 'int4')

    aggregator = 'sum'

    def _get_attrs(self, model_class, name):
        res = super()._get_attrs(model_class, name)
        # The default aggregator is None for sequence fields
        if 'aggregator' not in res and name == 'sequence':
            res['aggregator'] = None
        return res

    def convert_to_column(self, value, record, values=None, validate=True):
        return int(value or 0)

    def convert_to_column_update(self, value, record):
        if self.company_dependent:
            value = {k: int(v or 0) for k, v in value.items()}
        return super().convert_to_column_update(value, record)

    def convert_to_cache(self, value, record, validate=True):
        if isinstance(value, dict):
            # special case, when an integer field is used as inverse for a one2many
            return value.get('id', None)
        return int(value or 0)

    def convert_to_record(self, value, record):
        return value or 0

    def convert_to_read(self, value, record, use_display_name=True):
        # Integer values greater than 2^31-1 are not supported in pure XMLRPC,
        # so we have to pass them as floats :-(
        if value and value > MAXINT:
            return float(value)
        return value

    def _update(self, records, value):
        cache = records.env.cache
        for record in records:
            cache.set(record, self, value.id or 0)

    def convert_to_export(self, value, record):
        if value or value == 0:
            return value
        return ''


class Float(Field[float]):
    """ Encapsulates a :class:`float`.

    The precision digits are given by the (optional) ``digits`` attribute.

    :param digits: a pair (total, decimal) or a string referencing a
        :class:`~odoo.addons.base.models.decimal_precision.DecimalPrecision` record name.
    :type digits: tuple(int,int) or str

    When a float is a quantity associated with an unit of measure, it is important
    to use the right tool to compare or round values with the correct precision.

    The Float class provides some static methods for this purpose:

    :func:`~odoo.fields.Float.round()` to round a float with the given precision.
    :func:`~odoo.fields.Float.is_zero()` to check if a float equals zero at the given precision.
    :func:`~odoo.fields.Float.compare()` to compare two floats at the given precision.

    .. admonition:: Example

        To round a quantity with the precision of the unit of measure::

            fields.Float.round(self.product_uom_qty, precision_rounding=self.product_uom_id.rounding)

        To check if the quantity is zero with the precision of the unit of measure::

            fields.Float.is_zero(self.product_uom_qty, precision_rounding=self.product_uom_id.rounding)

        To compare two quantities::

            field.Float.compare(self.product_uom_qty, self.qty_done, precision_rounding=self.product_uom_id.rounding)

    """
    type = 'float'
    _column_type = ('float8', 'float8')

    aggregator = 'sum'

    # Float precision digits: (total, decimal) or string
    digits = None

    @staticmethod
    def round(value, precision_digits=None, precision_rounding=None):
        """ Return ``value`` rounded to the given precision. """
        return float_round(value, precision_digits=precision_digits, precision_rounding=precision_rounding)

    @staticmethod
    def is_zero(value, precision_digits=None, precision_rounding=None):
        """ Return whether ``value`` is zero at the given precision. """
        return float_is_zero(value, precision_digits=precision_digits, precision_rounding=precision_rounding)

    @staticmethod
    def compare(value1, value2, precision_digits=None, precision_rounding=None):
        """ Compare ``value1`` and ``value2`` at the given precision. """
        return float_compare(value1, value2, precision_digits=precision_digits, precision_rounding=precision_rounding)

    def convert_to_column(self, value, record, values=None, validate=True):
        result = float(value or 0.0)
        digits = getattr(self, 'digits', None) or getattr(self, '_digits', None)
        if digits:
            precision, scale = digits if isinstance(digits, tuple) else record.env['decimal.precision']._get_precision(digits)
            result = float_round(result, precision_digits=scale)
        return result

    def convert_to_column_update(self, value, record):
        if self.company_dependent:
            digits = getattr(self, 'digits', None) or getattr(self, '_digits', None)
            if digits:
                precision, scale = digits if isinstance(digits, tuple) else record.env['decimal.precision']._get_precision(digits)
                value = {k: float_round(float(v or 0.0), precision_digits=scale) for k, v in value.items()}
            else:
                value = {k: float(v or 0.0) for k, v in value.items()}
        return super().convert_to_column_update(value, record)

    def convert_to_cache(self, value, record, validate=True):
        # apply rounding here, otherwise value in cache may be wrong
        value = float(value or 0.0)
        digits = getattr(self, 'digits', None) or getattr(self, '_digits', None)
        return float_round(value, precision_digits=digits[1]) if digits else value

    def convert_to_export(self, value, record):
        if value or value == 0.0:
            return value
        return ''

    def convert_to_display_name(self, value, record):
        digits = getattr(self, 'digits', None) or getattr(self, '_digits', None)
        if digits:
            return float_repr(value, digits[1])
        return repr(value) if value else ''


class Monetary(Field[float]):
    """ Encapsulates a :class:`float` expressed in a given
    :class:`res_currency<odoo.addons.base.models.res_currency.Currency>`.

    The decimal precision and currency symbol are taken from the ``currency_field`` attribute.

    :param str currency_field: name of the many2one field holding the
        :class:`res_currency <odoo.addons.base.models.res_currency.Currency>` this monetary
        field is expressed in (default: `'currency_id'`)
    """
    type = 'monetary'
    _column_type = ('numeric', 'numeric')

    aggregator = 'sum'

    # currency field name
    currency_field = 'currency_id'

    def __init__(self, string: str | Sentinel = SENTINEL, currency_field: str = 'currency_id', **kwargs):
        super().__init__(string=string, currency_field=currency_field, **kwargs)

    def convert_to_column(self, value, record, values=None, validate=True):
        # retrieve currency from values or record
        if values and self.currency_field in values:
            currency_id = values[self.currency_field]
            currency = record.env['res.currency'].browse(currency_id)
        elif record and self.currency_field in record:
            currency = record[self.currency_field]
        else:
            currency = record.env['res.currency']

        return currency.round(float(value or 0.0)) if currency else float(value or 0.0)

    def convert_to_cache(self, value, record, validate=True):
        # apply rounding here, otherwise value in cache may be wrong
        value = float(value or 0.0)
        if record and self.currency_field in record:
            currency = record[self.currency_field]
            return currency.round(value) if currency else value
        return value

    def convert_to_export(self, value, record):
        if value or value == 0.0:
            return value
        return ''

    def convert_to_display_name(self, value, record):
        if record and self.currency_field in record:
            currency = record[self.currency_field]
            if currency:
                return currency.format(value)
        return str(value) if value else ''


class _String(Field[str | typing.Literal[False]]):
    """ Abstract class for string fields. """
    translate = False                   # whether the field is translated
    size = None                         # maximum size of values (deprecated)

    def __init__(self, string: str | Sentinel = SENTINEL, **kwargs):
        # The 'size' attribute is deprecated as of v8.0, and moved to class _String.
        if 'size' in kwargs and 'translate' not in kwargs:
            # translate is either True, False, or a callable
            kwargs.setdefault('translate', False)
        super().__init__(string=string, **kwargs)

    def _get_attrs(self, model_class, name):
        attrs = super()._get_attrs(model_class, name)
        # translate is either True, False, or a callable
        if attrs.get('translate'):
            attrs['copy'] = attrs.get('copy', False)
        return attrs

    def get_trans_terms(self, value):
        """ Return the sequence of terms to translate found in `value`. """
        if not self.translate or not value:
            return []
        if self.translate is True:
            return [value]
        if callable(self.translate):
            return self.translate(value)
        return []

    def convert_to_column(self, value, record, values=None, validate=True):
        if value is None or value is False:
            return None
        # we need to convert the string to the database encoding
        return str(value)

    def convert_to_cache(self, value, record, validate=True):
        if value is None or value is False:
            return False
        return str(value)

    def convert_to_record(self, value, record):
        return value if value else False

    def convert_to_export(self, value, record):
        return value if value else ''


class Char(_String):
    """ Basic string field, can be length-limited, usually displayed as a
    single-line string in clients.

    :param int size: the maximum size of values stored for that field

    :param bool trim: states whether the value is trimmed or not (by default,
        ``True``). Note that the trim operation is applied only by the web client.

    :param translate: enable the translation of the field's values; use
        ``translate=True`` to translate field values as a whole; ``translate``
        may also be a callable such that ``translate(callback, value)``
        translates ``value`` by using ``callback(term)`` to retrieve the
        translation of terms.
    :type translate: bool or callable
    """
    type = 'char'
    trim = True                         # whether value is trimmed (only by web client)

    def _setup_attrs(self, model_class, name):
        super()._setup_attrs(model_class, name)
        assert self.size is None or isinstance(self.size, int), \
            "Char field %s with non-integer size %r" % (self, self.size)

    @property
    def _column_type(self):
        return ('varchar', pg_varchar(self.size))

    def update_db_column(self, model, column):
        if (
            column and self.column_type[0] == 'varchar' and
            column['udt_name'] == 'varchar' and column['character_maximum_length'] and
            (self.size is None or column['character_maximum_length'] < self.size)
        ):
            # the column's varchar size does not match self.size; convert it
            sql.convert_column(model._cr, model._table, self.name, self.column_type[1])
        super().update_db_column(model, column)

    _related_size = property(attrgetter('size'))
    _related_trim = property(attrgetter('trim'))
    _description_size = property(attrgetter('size'))
    _description_trim = property(attrgetter('trim'))


class Text(_String):
    """ Very similar to :class:`Char` but used for longer contents, does not
    have a size and usually displayed as a multiline text box.

    :param translate: enable the translation of the field's values; use
        ``translate=True`` to translate field values as a whole; ``translate``
        may also be a callable such that ``translate(callback, value)``
        translates ``value`` by using ``callback(term)`` to retrieve the
        translation of terms.
    :type translate: bool or callable
    """
    type = 'text'
    _column_type = ('text', 'text')


class Html(_String):
    """ Encapsulates an html code content.

    :param bool sanitize: whether value must be sanitized (default: ``True``)
    :param bool sanitize_overridable: whether the sanitation can be bypassed by
        the users part of the `base.group_sanitize_override` group (default: ``False``)
    :param bool sanitize_tags: whether to sanitize tags
        (only a white list of attributes is accepted, default: ``True``)
    :param bool sanitize_attributes: whether to sanitize attributes
        (only a white list of attributes is accepted, default: ``True``)
    :param bool sanitize_style: whether to sanitize style attributes (default: ``False``)
    :param bool sanitize_conditional_comments: whether to kill conditional comments. (default: ``True``)
    :param bool sanitize_output_method: whether to sanitize using html or xhtml (default: ``html``)
    :param bool strip_style: whether to strip style attributes
        (removed and therefore not sanitized, default: ``False``)
    :param bool strip_classes: whether to strip classes attributes (default: ``False``)
    """
    type = 'html'
    _column_type = ('text', 'text')

    sanitize = True                     # whether value must be sanitized
    sanitize_overridable = False        # whether the sanitation can be bypassed by the users part of the `base.group_sanitize_override` group
    sanitize_tags = True                # whether to sanitize tags (only a white list of attributes is accepted)
    sanitize_attributes = True          # whether to sanitize attributes (only a white list of attributes is accepted)
    sanitize_style = False              # whether to sanitize style attributes
    sanitize_form = True                # whether to sanitize forms
    sanitize_conditional_comments = True  # whether to kill conditional comments. Otherwise keep them but with their content sanitized.
    sanitize_output_method = 'html'     # whether to sanitize using html or xhtml
    strip_style = False                 # whether to strip style attributes (removed and therefore not sanitized)
    strip_classes = False               # whether to strip classes attributes

    def _get_attrs(self, model_class, name):
        # called by _setup_attrs(), working together with _String._setup_attrs()
        attrs = super()._get_attrs(model_class, name)
        # Shortcut for common sanitize options
        # Outgoing and incoming emails should not be sanitized with the same options.
        # e.g. conditional comments: no need to keep conditional comments for incoming emails,
        # we do not need this Microsoft Outlook client feature for emails displayed Odoo's web client.
        # While we need to keep them in mail templates and mass mailings, because they could be rendered in Outlook.
        if attrs.get('sanitize') == 'email_outgoing':
            attrs['sanitize'] = True
            attrs.update({key: value for key, value in {
                'sanitize_tags': False,
                'sanitize_attributes': False,
                'sanitize_conditional_comments': False,
                'sanitize_output_method': 'xml',
            }.items() if key not in attrs})
        # Translated sanitized html fields must use html_translate or a callable.
        # `elif` intended, because HTML fields with translate=True and sanitize=False
        # where not using `html_translate` before and they must remain without `html_translate`.
        # Otherwise, breaks `--test-tags .test_render_field`, for instance.
        elif attrs.get('translate') is True and attrs.get('sanitize', True):
            attrs['translate'] = html_translate
        return attrs

    _related_sanitize = property(attrgetter('sanitize'))
    _related_sanitize_tags = property(attrgetter('sanitize_tags'))
    _related_sanitize_attributes = property(attrgetter('sanitize_attributes'))
    _related_sanitize_style = property(attrgetter('sanitize_style'))
    _related_sanitize_form = property(attrgetter('sanitize_form'))
    _related_strip_style = property(attrgetter('strip_style'))
    _related_strip_classes = property(attrgetter('strip_classes'))
