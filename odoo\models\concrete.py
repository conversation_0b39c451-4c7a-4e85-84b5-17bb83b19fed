# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Concrete model classes for Odoo.

This module contains the concrete model classes (Model, TransientModel)
extracted from the monolithic models.py file.
"""

from .. import api
from .base import BaseModel


class Model(BaseModel):
    """ Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    """
    _auto = True                # automatically create database backend
    _register = False           # not visible in ORM registry, meant to be python-inherited only
    _abstract = False           # not abstract
    _transient = False          # not transient


class TransientModel(BaseModel):
    """ Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    """
    _auto = True                # automatically create database backend
    _register = False           # not visible in ORM registry, meant to be python-inherited only
    _abstract = False           # not abstract
    _transient = True           # transient

    @api.autovacuum
    def _transient_vacuum(self):
        """Clean the transient records.

        This unlinks old records from the transient model tables whenever the
        "_transient_max_count" or "_transient_max_hours" conditions (if any) are
        reached.
        Actual cleaning will happen only once every "_transient_check_time" calls.
        This means this method can be called frequently called (e.g. whenever
        a new record is created).
        Note that this method is called automatically by the ORM each time a record
        is created in a transient model.
        """
        if not self._transient:
            return

        # determine max count and max hours
        max_count = getattr(self, '_transient_max_count', 0)
        max_hours = getattr(self, '_transient_max_hours', 0)

        if max_count:
            self._transient_clean_old_rows(max_count)

        if max_hours:
            self._transient_clean_rows_older_than(max_hours * 3600)

    def _transient_clean_old_rows(self, max_count):
        # Check how many rows we have in the table
        self.env.cr.execute(f"SELECT COUNT(*) FROM {self._table}")
        [count] = self.env.cr.fetchone()
        if count > max_count:
            # Delete the oldest rows
            self.env.cr.execute(f"""
                DELETE FROM {self._table}
                WHERE id IN (
                    SELECT id FROM {self._table}
                    ORDER BY id
                    LIMIT %s
                )
            """, [count - max_count])

    def _transient_clean_rows_older_than(self, seconds):
        # Never delete rows used in last 5 minutes
        seconds = max(seconds, 300)
        
        # Delete rows older than the specified time
        self.env.cr.execute(f"""
            DELETE FROM {self._table}
            WHERE write_date < (now() at time zone 'UTC' - interval '%s seconds')
        """, [seconds])


# Utility functions for error handling
def convert_pgerror_not_null(model, fields, info, e):
    """Convert PostgreSQL not-null error to user-friendly message."""
    env = model.env
    field_name = e.diag.column_name
    field = fields.get(field_name)
    if field:
        message = _(
            "The field %(field_name)s is required. Please provide a value.",
            field_name=field.string or field_name
        )
    else:
        message = _(
            "A required field is missing. Please check your data."
        )
    raise ValidationError(message) from e


def convert_pgerror_unique(model, fields, info, e):
    """Convert PostgreSQL unique constraint error to user-friendly message."""
    # Extract constraint name from error
    constraint_name = e.diag.constraint_name
    
    # Look up the constraint in model's _sql_constraints
    sql_constraints = dict([
        (f'{e.diag.table_name}_{x[0]}', x) 
        for x in model._sql_constraints
    ])
    
    if constraint_name in sql_constraints:
        _, _, message = sql_constraints[constraint_name]
        raise ValidationError(message) from e
    else:
        # Generic unique constraint error
        message = _(
            "The record you are trying to create already exists. "
            "Please check for duplicates."
        )
        raise ValidationError(message) from e


def convert_pgerror_constraint(model, fields, info, e):
    """Convert PostgreSQL constraint error to user-friendly message."""
    constraint_name = e.diag.constraint_name
    
    # Look up the constraint in model's _sql_constraints
    sql_constraints = dict([
        (f'{e.diag.table_name}_{x[0]}', x) 
        for x in model._sql_constraints
    ])
    
    if constraint_name in sql_constraints:
        _, _, message = sql_constraints[constraint_name]
        raise ValidationError(message) from e
    else:
        # Generic constraint error
        message = _(
            "The operation cannot be completed due to data constraints. "
            "Please check your data."
        )
        raise ValidationError(message) from e
