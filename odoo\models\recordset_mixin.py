# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Recordset operations mixin for Odoo models.

This module contains recordset manipulation functionality extracted from the monolithic models.py file.
"""

import typing
from .. import api

if typing.TYPE_CHECKING:
    from odoo.api import Self


class RecordsetMixin:
    """Mixin class containing recordset operations for BaseModel."""

    def __bool__(self):
        """ Test whether ``self`` is nonempty. """
        return bool(self._ids)

    def __len__(self):
        """ Return the size of ``self``. """
        return len(self._ids)

    def __iter__(self) -> typing.Iterator['Self']:
        """ Return an iterator over ``self``. """
        if self._prefetch_ids:
            prefetch_ids = self._prefetch_ids
        else:
            prefetch_ids = self._ids

        for id in self._ids:
            yield self.__class__(self.env, (id,), prefetch_ids)

    def __reversed__(self) -> typing.Iterator['Self']:
        """ Return an reversed iterator over ``self``. """
        if self._prefetch_ids:
            prefetch_ids = self._prefetch_ids
        else:
            prefetch_ids = self._ids

        for id in reversed(self._ids):
            yield self.__class__(self.env, (id,), prefetch_ids)

    def __contains__(self, item):
        """ Test whether ``item`` (record or field name) is an element of ``self``.
        In the first case, the test is fully equivalent to::

            any(item == record for record in self)
        """
        if isinstance(item, BaseModel):
            if self._name == item._name:
                return len(item) == 1 and item.id in self._ids
            raise TypeError(f"Mixing apples and oranges: {self._name} vs {item._name}")
        elif isinstance(item, str):
            return item in self._fields
        else:
            return item in self._ids

    def __add__(self, other) -> 'Self':
        """ Return the concatenation of two recordsets. """
        return self.concat(other)

    @api.private
    def concat(self, *args) -> 'Self':
        """ Return the concatenation of ``self`` with all the arguments (in
        linear time complexity).
        """
        ids = list(self._ids)
        for arg in args:
            if not isinstance(arg, BaseModel) or arg._name != self._name:
                raise TypeError(f"Mixing apples and oranges: {self._name} vs {getattr(arg, '_name', type(arg))}")
            ids.extend(arg._ids)

        return self.browse(ids)

    def __sub__(self, other) -> 'Self':
        """ Return the recordset of all the records in ``self`` that are not in
        ``other``. Equivalent to recordset filtering::

            self.filtered(lambda r: r not in other)
        """
        if not isinstance(other, BaseModel) or other._name != self._name:
            raise TypeError(f"Mixing apples and oranges: {self._name} vs {getattr(other, '_name', type(other))}")
        
        other_ids = set(other._ids)
        ids = [id for id in self._ids if id not in other_ids]
        return self.browse(ids)

    def __and__(self, other) -> 'Self':
        """ Return the intersection of two recordsets.
        Equivalent to recordset filtering::

            self.filtered(lambda r: r in other)
        """
        if not isinstance(other, BaseModel) or other._name != self._name:
            raise TypeError(f"Mixing apples and oranges: {self._name} vs {getattr(other, '_name', type(other))}")
        
        other_ids = set(other._ids)
        ids = [id for id in self._ids if id in other_ids]
        return self.browse(ids)

    def __or__(self, other) -> 'Self':
        """ Return the union of two recordsets.
        Equivalent to concatenation without duplicates::

            (self + other).distinct()
        """
        return self.union(other)

    @api.private
    def union(self, *args) -> 'Self':
        """ Return the union of ``self`` with all the arguments (in linear time
        complexity, with duplicates removed).
        """
        ids = list(self._ids)
        seen = set(ids)
        
        for arg in args:
            if not isinstance(arg, BaseModel) or arg._name != self._name:
                raise TypeError(f"Mixing apples and oranges: {self._name} vs {getattr(arg, '_name', type(arg))}")
            for id in arg._ids:
                if id not in seen:
                    ids.append(id)
                    seen.add(id)

        return self.browse(ids)

    def __eq__(self, other):
        """ Test whether two recordsets are equivalent (up to reordering). """
        if not isinstance(other, BaseModel):
            if other is False or other is None:
                return not self
            return NotImplemented
        if self._name != other._name:
            return False
        return set(self._ids) == set(other._ids)

    def __lt__(self, other):
        try:
            return self.id < other.id
        except (AttributeError, TypeError):
            return NotImplemented

    def __le__(self, other):
        try:
            return self.id <= other.id
        except (AttributeError, TypeError):
            return NotImplemented

    def __gt__(self, other):
        try:
            return self.id > other.id
        except (AttributeError, TypeError):
            return NotImplemented

    def __ge__(self, other):
        try:
            return self.id >= other.id
        except (AttributeError, TypeError):
            return NotImplemented

    def __int__(self):
        return self.id or 0

    def __repr__(self):
        return f"{self._name}{self._ids!r}"

    def __hash__(self):
        return hash((self._name, frozenset(self._ids)))

    def __deepcopy__(self, memo):
        return self

    def __getitem__(self, key):
        """ If ``key`` is an integer or a slice, return the corresponding record
        selection as an instance (attached to the same environment).
        Otherwise read the field ``key`` of the first record in ``self``.

        Examples::

            inst = model.search(dom)    # inst is a recordset
            first_record = inst[0]      # first_record is a record
            fifth_record = inst[4]      # fifth_record is a record
            sub_records = inst[1:3]     # sub_records is a recordset
            name = first_record['name'] # name is a field value
        """
        if isinstance(key, str):
            # field access
            self.ensure_one()
            return self._fields[key].__get__(self, type(self))
        elif isinstance(key, slice):
            # slice access
            ids = self._ids[key]
            return self.browse(ids)
        elif isinstance(key, int):
            # index access
            return self.browse([self._ids[key]])
        else:
            raise TypeError(f"Invalid key type: {type(key)}")

    def __setitem__(self, key, value):
        """ Assign the field ``key`` to ``value`` in record ``self``. """
        # important: one must call the field's setter
        return self._fields[key].__set__(self, value)

    @api.private
    def ensure_one(self) -> 'Self':
        """Verify that the current recordset holds a single record.

        :raise ValueError: if the recordset does not contain exactly one record
        :return: ``self``
        """
        try:
            # unpack to ensure there's exactly one value
            [id] = self._ids
            return self
        except ValueError:
            raise ValueError(f"Expected singleton: {self!r}")

    @api.private
    def mapped(self, func):
        """Apply ``func`` on all records in ``self``, and return the result as a
        list or a recordset (if ``func`` return recordsets). In the latter
        case, the order of the returned recordset is arbitrary.

        :param func: a function or a dot-separated sequence of field names
        :return: self.env[model]() (empty recordset of model ``model``) if
                 the function/field does not exist or is not set on any record
        """
        if isinstance(func, str):
            # field path
            return self._mapped_func(lambda rec: rec[func])
        else:
            # function
            return self._mapped_func(func)

    def _mapped_func(self, func):
        """ Apply function ``func`` on all records in ``self``, and return the
        result as a list or a recordset.
        """
        vals = [func(rec) for rec in self]
        
        # determine the result type
        if vals and isinstance(vals[0], BaseModel):
            # recordset result
            result = vals[0].browse()
            for val in vals:
                if val:
                    result |= val
            return result
        else:
            # list result
            return vals

    @api.private
    def filtered(self, func) -> 'Self':
        """Return the records in ``self`` satisfying ``func``.

        :param func: a function or a dot-separated sequence of field names
        :return: recordset of records satisfying func
        """
        if isinstance(func, str):
            # field path
            return self.filtered(lambda rec: rec[func])
        else:
            # function
            ids = [rec.id for rec in self if func(rec)]
            return self.browse(ids)

    @api.private
    def grouped(self, key):
        """Eagerly groups the records of ``self`` by the ``key``, returning a
        dictionary mapping each key value to a recordset of records with that
        key value.

        :param key: either a function or a field name
        :return: dict mapping key values to recordsets
        """
        if isinstance(key, str):
            # field name
            key_func = lambda rec: rec[key]
        else:
            # function
            key_func = key

        groups = {}
        for record in self:
            k = key_func(record)
            if k not in groups:
                groups[k] = self.browse()
            groups[k] |= record

        return groups

    @api.private
    def sorted(self, key=None, reverse=False) -> 'Self':
        """Return the recordset ``self`` ordered by ``key``.

        :param key: either a function or a field name
        :param reverse: if ``True``, return the result in reverse order
        :return: ordered recordset
        """
        if key is None:
            # sort by id
            ids = sorted(self._ids, reverse=reverse)
        elif isinstance(key, str):
            # field name
            recs = sorted(self, key=lambda rec: rec[key], reverse=reverse)
            ids = [rec.id for rec in recs]
        else:
            # function
            recs = sorted(self, key=key, reverse=reverse)
            ids = [rec.id for rec in recs]

        return self.browse(ids)

    def update(self, values):
        """ Update the records in ``self`` with ``values``. """
        for record in self:
            record.write(values)

    @api.private
    def with_env(self, env) -> 'Self':
        """Return a new version of this recordset attached to the provided environment.

        :param env: new environment for the recordset
        :return: recordset in the new environment
        """
        return self.__class__(env, self._ids, self._prefetch_ids)

    @api.private
    def sudo(self, flag=True) -> 'Self':
        """ sudo([flag=True])

        Return a new version of this recordset with superuser mode enabled or
        disabled, depending on `flag`.

        :param flag: whether to enable or disable superuser mode.
        :return: recordset with modified superuser mode
        """
        return self.with_env(self.env(su=flag))

    @api.private
    def with_user(self, user) -> 'Self':
        """ with_user(user)

        Return a new version of this recordset with a different user.

        :param user: new user for the recordset
        :return: recordset with the new user
        """
        return self.with_env(self.env(user=user))

    @api.private
    def with_company(self, company) -> 'Self':
        """ with_company(company)

        Return a new version of this recordset with a different company in the context.

        :param company: main company of the new recordset.
        :return: recordset with the new company
        """
        context = dict(self.env.context)
        if company:
            context['allowed_company_ids'] = company.ids
            context['company_id'] = company.id
        else:
            context.pop('allowed_company_ids', None)
            context.pop('company_id', None)
        
        return self.with_env(self.env(context=context))

    @api.private
    def with_context(self, *args, **kwargs) -> 'Self':
        """ with_context([context][, **overrides]) -> Model

        Return a new version of this recordset with an altered context.

        :param context: new context to use (optional)
        :param overrides: context keys to override
        :return: recordset with the new context
        """
        if args:
            context = args[0]
            if kwargs:
                context = dict(context, **kwargs)
        else:
            context = dict(self.env.context, **kwargs)
        
        return self.with_env(self.env(context=context))

    @api.private
    def with_prefetch(self, prefetch_ids=None) -> 'Self':
        """ with_prefetch([prefetch_ids]) -> records

        Return a new version of this recordset with the given prefetch ids.

        :param prefetch_ids: ids to prefetch (default: self._ids)
        :return: recordset with the new prefetch ids
        """
        if prefetch_ids is None:
            prefetch_ids = self._ids
        return self.__class__(self.env, self._ids, prefetch_ids)
